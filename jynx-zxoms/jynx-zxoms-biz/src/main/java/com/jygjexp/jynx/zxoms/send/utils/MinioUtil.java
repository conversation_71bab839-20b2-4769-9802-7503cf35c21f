package com.jygjexp.jynx.zxoms.send.utils;

import io.minio.*;
import io.minio.errors.*;
import io.minio.messages.Bucket;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.List;

@Component
public class MinioUtil {

    private static final String endpoint = "http://minio.neighbourexpress.test/";
    private static String viewEndpoint ="oss.neighbourexpress.ca";
    private static final String accessKey = "yf8aJI01HyL2PLXbGgfq";
    private static final String secretKey = "zhVGyej86rdtz2vmobuw05L00xSQYWrbfrxAgJA7";
    private static final String bucketName = "nbexpress";

    private final MinioClient minioClient;

    // 初始化 MinIO 客户端
    public MinioUtil() {
        this.minioClient = MinioClient.builder()
                .endpoint(endpoint)
                .credentials(accessKey, secretKey)
                .build();
    }

    /**
     * 创建存储桶
     */
    public void createBucket(String bucketName) throws Exception {
        if (!minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build())) {
            minioClient.makeBucket(MakeBucketArgs.builder().bucket(bucketName).build());
        }
    }

    /**
     * 列出所有存储桶
     */
    public List<Bucket> listBuckets() throws Exception {
        return minioClient.listBuckets();
    }

    /**
     * 上传文件
     */
    public String uploadFile(MultipartFile file, String bucketName) throws Exception {
        createBucket(bucketName);

        String objectName = System.currentTimeMillis() + "_" + file.getOriginalFilename();
        InputStream inputStream = file.getInputStream();

        minioClient.putObject(
                PutObjectArgs.builder()
                        .bucket(bucketName)
                        .object(objectName)
                        .stream(inputStream, file.getSize(), -1)
                        .contentType(file.getContentType())
                        .build()
        );

        return endpoint + "/" + bucketName + "/" + objectName;
    }

    /**
     * 下载文件
     */
    public InputStream downloadFile(String bucketName, String objectName) throws Exception {
        return minioClient.getObject(
                GetObjectArgs.builder()
                        .bucket(bucketName)
                        .object(objectName)
                        .build()
        );
    }

    /**
     * 删除文件
     */
    public void deleteFile(String bucketName, String objectName) throws Exception {
        minioClient.removeObject(
                RemoveObjectArgs.builder()
                        .bucket(bucketName)
                        .object(objectName)
                        .build()
        );
    }

    /**
     * 列出存储桶中的所有文件
     */
//    public List<String> listFiles(String bucketName) throws Exception {
//        return minioClient.listObjects(
//                ListObjectsArgs.builder().bucket(bucketName).build()
//        ).stream().map(item -> {
//            try {
//                return item.get().objectName();
//            } catch (Exception e) {
//                throw new RuntimeException(e);
//            }
//        }).toList();
//    }
}

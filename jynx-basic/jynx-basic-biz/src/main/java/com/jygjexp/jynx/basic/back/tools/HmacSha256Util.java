package com.jygjexp.jynx.basic.back.tools;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.util.Base64;

public class HmacSha256Util {

    // HMAC 签名秘钥
    private static final String SECRET_KEY = "mySecretKey123";

    // 生成签名
    public static String generateSign(String data) throws Exception {
        Mac mac = Mac.getInstance("HmacSHA256");
        SecretKeySpec keySpec = new SecretKeySpec(SECRET_KEY.getBytes("UTF-8"), "HmacSHA256");
        mac.init(keySpec);
        byte[] hash = mac.doFinal(data.getBytes("UTF-8"));
        return Base64.getUrlEncoder().withoutPadding().encodeToString(hash);
    }

    // 校验签名
    public static boolean verifySign(String data, String sign) throws Exception {
        String calculated = generateSign(data);
        return calculated.equals(sign);
    }
}

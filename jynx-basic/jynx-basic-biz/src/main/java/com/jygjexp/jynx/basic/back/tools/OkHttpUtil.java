package com.jygjexp.jynx.basic.back.tools;

import okhttp3.*;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.TimeUnit;

public class OkHttpUtil {

    // 默认 MediaType
    private static final MediaType JSON = MediaType.parse("application/json; charset=utf-8");

    // 共享的连接池（避免频繁创建客户端）
    private static final ConnectionPool SHARED_POOL = new ConnectionPool(50, 5, TimeUnit.MINUTES);

    // 基础客户端（可 clone 覆盖超时）
    private static final OkHttpClient BASE_CLIENT = new OkHttpClient.Builder()
            .connectionPool(SHARED_POOL)
            .retryOnConnectionFailure(true)
            .connectTimeout(10, TimeUnit.SECONDS)
            .readTimeout(15, TimeUnit.SECONDS)
            .writeTimeout(15, TimeUnit.SECONDS)
            .build();

    /**
     * 通用请求方法。
     *
     * @param method       GET/POST/PUT/PATCH/DELETE/HEAD
     * @param url          完整URL（可不带查询参数）
     * @param queryParams  查询参数（可为空）
     * @param headers      请求头（可为空）
     * @param body         请求体（GET/HEAD会忽略；POST/PUT/PATCH若为空会自动用空Body）
     * @param connectMs    连接超时ms（<=0 表示使用默认）
     * @param readMs       读超时ms（<=0 表示使用默认）
     * @param writeMs      写超时ms（<=0 表示使用默认）
     * @return 响应字符串（非2xx会抛出IOException）
     * @throws IOException 网络或非2xx状态
     */
    public static String request(
            String method,
            String url,
            Map<String, ?> queryParams,
            Map<String, String> headers,
            RequestBody body,
            int connectMs,
            int readMs,
            int writeMs
    ) throws IOException {

        if (method == null || method.trim().isEmpty()) {
            method = "GET";
        }
        method = method.toUpperCase();

        // 构建URL + Query
        HttpUrl parsed = HttpUrl.parse(url);
        if (parsed == null) {
            throw new IllegalArgumentException("非法URL: " + url);
        }
        HttpUrl.Builder urlBuilder = parsed.newBuilder();
        if (queryParams != null) {
            for (Map.Entry<String, ?> e : queryParams.entrySet()) {
                if (e.getKey() != null && e.getValue() != null) {
                    urlBuilder.addQueryParameter(e.getKey(), String.valueOf(e.getValue()));
                }
            }
        }
        HttpUrl finalUrl = urlBuilder.build();

        // 构建Headers
        Headers.Builder hb = new Headers.Builder();
        if (headers != null) {
            for (Map.Entry<String, String> e : headers.entrySet()) {
                if (e.getKey() != null && e.getValue() != null) {
                    hb.add(e.getKey(), e.getValue());
                }
            }
        }

        // GET/HEAD 忽略body；POST/PUT/PATCH 若 body==null 则给个空Body
        RequestBody finalBody = body;
        if (("POST".equals(method) || "PUT".equals(method) || "PATCH".equals(method)) && finalBody == null) {
            finalBody = RequestBody.create(new byte[0], null);
        }
        if ("GET".equals(method) || "HEAD".equals(method)) {
            finalBody = null;
        }

        Request req = new Request.Builder()
                .url(finalUrl)
                .headers(hb.build())
                .method(method, finalBody)
                .build();

        // 每次调用可覆盖超时，但共享连接池
        OkHttpClient client = BASE_CLIENT;
        if (connectMs > 0 || readMs > 0 || writeMs > 0) {
            OkHttpClient.Builder cb = BASE_CLIENT.newBuilder();
            if (connectMs > 0) cb.connectTimeout(connectMs, TimeUnit.MILLISECONDS);
            if (readMs > 0) cb.readTimeout(readMs, TimeUnit.MILLISECONDS);
            if (writeMs > 0) cb.writeTimeout(writeMs, TimeUnit.MILLISECONDS);
            client = cb.build();
        }

        try (Response resp = client.newCall(req).execute()) {
            if (!resp.isSuccessful()) {
                String bodyStr = resp.body() != null ? resp.body().string() : "";
                throw new IOException("HTTP " + resp.code() + " - " + resp.message() + " | body: " + bodyStr);
            }
            return resp.body() != null ? resp.body().string() : "";
        }
    }

    /*================= 常用便捷方法 =================*/

    public static String get(String url) throws IOException {
        return request("GET", url, null, null, null, 0, 0, 0);
    }

    public static String get(String url, Map<String, ?> query) throws IOException {
        return request("GET", url, query, null, null, 0, 0, 0);
    }

    public static String get(String url, Map<String, ?> query, Map<String, String> headers) throws IOException {
        return request("GET", url, query, headers, null, 0, 0, 0);
    }

    public static String postJson(String url, String json, Map<String, String> headers) throws IOException {
        RequestBody body = RequestBody.create(json == null ? "" : json, JSON);
        return request("POST", url, null, headers, body, 0, 0, 0);
    }

    public static String postForm(String url, Map<String, String> form, Map<String, String> headers) throws IOException {
        FormBody.Builder fb = new FormBody.Builder();
        if (form != null) {
            for (Map.Entry<String, String> e : form.entrySet()) {
                if (e.getKey() != null && e.getValue() != null) {
                    fb.add(e.getKey(), e.getValue());
                }
            }
        }
        return request("POST", url, null, headers, fb.build(), 0, 0, 0);
    }
}

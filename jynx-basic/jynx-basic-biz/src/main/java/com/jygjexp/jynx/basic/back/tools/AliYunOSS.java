package com.jygjexp.jynx.basic.back.tools;

import cn.hutool.core.util.StrUtil;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.GetObjectRequest;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.PutObjectRequest;
import org.apache.commons.fileupload.FileUploadException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * 图片上传接口
 */
public class AliYunOSS {
    protected static final Logger log = LoggerFactory.getLogger(AliYunOSS.class);
    private static String endpoint = "oss-us-east-1-internal.aliyuncs.com";  //生产环境
    private static String viewEndpoint ="oss.neighbourexpress.ca";
    private static final String accessKeyId = "LTAI4GHBMPW2sZ7AiSVQNfMg";
    private static final String accessKeySecret = "******************************";
    private static final String bucketName = "nbexpress";


    //本地文件上传OSS
    public static String sendToOssTwo(MultipartFile file, String dir, String fileName) throws IOException {
        OSS ossClient = null;
        InputStream inputStream = null;
        try {
            log.info("开始处理图片上传：{}", file.getOriginalFilename());

            // 检查文件大小（比如限制为10MB）
            long fileSize = file.getSize();
            if (fileSize > 10 * 1024 * 1024) {  // 10MB
                throw new RuntimeException("图片大小超过限制, 最大允许上传 10MB");
            }

            // 获取文件输入流
            inputStream = file.getInputStream();

            // 设置上传的路径
            String objectName = null;  // 定义上传到 OSS 的路径
            //如果传入文件夹为空，则使用默认文件夹
            if (StrUtil.isBlank(dir)) {
                objectName = "imageInformation/" + fileName;  // 定义上传到 OSS 的路径
            } else {
                // 去除多余的斜杠
                objectName = dir.replaceAll("/$", "") + "/" + fileName;
            }

            // 上传到 OSS
            log.info("开始上传文件到 OSS...");

            //判断当前环境，测试用
            String osName = System.getProperty("os.name").toLowerCase();
            if (osName.contains("win")) {
                endpoint = "oss-us-east-1.aliyuncs.com";
            }

            ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
            ossClient.putObject(new PutObjectRequest(bucketName, objectName, inputStream));
            ossClient.shutdown();

            // 返回文件的访问 URL
            String url = "https://" + bucketName + "." + endpoint + "/" + objectName;
            if (url.contains("-internal")) {
                url = url.replace("-internal", "");
            }
            log.info("文件上传成功，访问地址：{}", url);
            return url;

        } catch (IOException e) {
            log.error("文件上传失败", e);
            throw new RuntimeException("上传失败", e);
        } finally {
            // 关闭流
            if (ossClient != null) {
                ossClient.shutdown();  // 关闭 OSS 客户端连接
            }
            if (inputStream != null) {
                try {
                    inputStream.close();  // 确保在上传完成后再关闭输入流
                } catch (IOException e) {
                    log.warn("关闭输入流时发生异常", e);
                }
            }
        }
    }

    public static String sendToOssFromByteArray(byte[] fileBytes, String dir, String fileName) throws Exception {
        OSS ossClient = null;
        ByteArrayInputStream inputStream = null;
        try {
            log.info("开始处理文件上传：{}", fileName);

            // 设置上传的路径
            String objectName = null;  // 定义上传到 OSS 的路径
            if (StrUtil.isBlank(dir)) {
                objectName = "pickup_exports/" + fileName;  // 定义上传到 OSS 的路径
            } else {
                objectName = dir.replaceAll("/$", "") + "/" + fileName;
            }

            // 上传到 OSS
            log.info("开始上传文件到 OSS...");

            // 判断当前环境，测试用
            String osName = System.getProperty("os.name").toLowerCase();
            if (osName.contains("win")) {
                endpoint = "oss-us-east-1.aliyuncs.com";
            }

            ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);

            // 创建字节输入流
            inputStream = new ByteArrayInputStream(fileBytes);

            // 上传文件
            ossClient.putObject(new PutObjectRequest(bucketName, objectName, inputStream));
            ossClient.shutdown();

            // 返回文件的访问 URL
            String url = "https://"+ viewEndpoint + "/" + objectName;
            log.info("文件上传成功，访问地址：{}", url);
            return url;
        } catch (Exception e) {
            log.error("文件上传失败", e);
            throw new RuntimeException("上传失败", e);
        } finally {
            // 关闭流
            if (ossClient != null) {
                ossClient.shutdown();  // 关闭 OSS 客户端连接
            }
            if (inputStream != null) {
                try {
                    inputStream.close();  // 确保在上传完成后再关闭输入流
                } catch (IOException e) {
                    log.warn("关闭输入流时发生异常", e);
                }
            }
        }
    }

    // 从OSS获取文件流
    public static InputStream getFileStreamFromOss(String fileUrl) throws IOException {
        OSS ossClient = null;
        try {
            // 提取文件的路径和文件名
            log.info("开始处理文件下载：{}", fileUrl);
//            String objectName = fileUrl.substring(fileUrl.lastIndexOf("/") + 1);
            String objectName = fileUrl.substring(fileUrl.indexOf(bucketName) + bucketName.length() + 1);
            objectName = objectName.replace("oss-us-east-1.aliyuncs.com/", "");  // 去掉域名部分

            // 去掉内网域名中的 "-internal" 部分  使用公网进行下载
            if (objectName.contains("oss-us-east-1-internal.aliyuncs.com/")) {
                objectName = objectName.replace("oss-us-east-1-internal.aliyuncs.com/", "");
            }

            log.info("objectName:{}", objectName);

            // 修改endpoint，使用公网进行下载
            String endpointNew = endpoint.replace("oss-us-east-1-internal.aliyuncs.com", "oss-us-east-1.aliyuncs.com");
            // 创建OSS客户端
            ossClient = new OSSClientBuilder().build(endpointNew, accessKeyId, accessKeySecret);

            // 从OSS获取文件
            OSSObject ossObject = ossClient.getObject(new GetObjectRequest(bucketName, objectName));
            return ossObject.getObjectContent();
        } catch (Exception e) {
            log.error("从OSS获取文件失败", e);
            throw new IOException("获取文件流失败", e);
        }
    }

}

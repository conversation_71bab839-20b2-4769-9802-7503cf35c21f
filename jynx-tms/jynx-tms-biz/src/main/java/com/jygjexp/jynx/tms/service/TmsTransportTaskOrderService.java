package com.jygjexp.jynx.tms.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.dto.TmsScheduleAppointDto;
import com.jygjexp.jynx.tms.dto.app.ListDeliveryOrderDTO;
import com.jygjexp.jynx.tms.entity.TmsCustomerOrderEntity;
import com.jygjexp.jynx.tms.entity.TmsDriverEntity;
import com.jygjexp.jynx.tms.entity.TmsLmdDriverEntity;
import com.jygjexp.jynx.tms.entity.TmsTransportTaskOrderEntity;
import com.jygjexp.jynx.tms.vo.TmsLmdDriverPageVo;
import com.jygjexp.jynx.tms.vo.TmsTransportTaskOrderPageVo;
import com.jygjexp.jynx.tms.vo.TmsTransportTaskOrderVo;
import com.jygjexp.jynx.tms.vo.excel.TmsTransportDeliveryTaskExcelVo;
import com.jygjexp.jynx.tms.vo.excel.TmsTransportTaskExcelVo;

import java.util.List;

public interface TmsTransportTaskOrderService extends IService<TmsTransportTaskOrderEntity> {

    // 创建运输任务单
    R createTransportTaskOrder(TmsCustomerOrderEntity customerOrderEntity);

    // 分页查询运输任务单
    Page<TmsTransportTaskOrderPageVo> search(Page page, TmsTransportTaskOrderPageVo vo);

    //查询揽收任务单（全部、待指派、已指派）
    R getAllTmsTransportTaskOrder(Page page, TmsTransportTaskOrderVo tmsTransportTaskOrderVo);

    // 根据id查询详情
    R selectById(Long id);

    // 根据订单id查询详情
    R selectByOrderId(Long id,Integer isTask);

    // 多客户单生成揽收任务单
    List<Long> createPickupTaskOrder(List<TmsCustomerOrderEntity> orderList, Long driverId);

    //通过订单号查询司机
    List<TmsLmdDriverPageVo> getDriverByOrderNo(String orderNo);

    // 揽收指派
    R collectAppointNew(TmsScheduleAppointDto appointDto);

    /**
     * 根据客户单号查询订单详情
     */
    R getCustomerOrderByCustomerOrderNumber(String customerOrderNumber, String siteId);

    /**
     * 根据任务类型查询已完成的揽收任务或者派送任务
     */
    R getFinishCollectOrDeliveryTask(Page page, Integer taskType);

    /**
     * 根据派送任务批次号查询该任务中的所有订单
     */
    R getDriverDeliveryOrderByTaskOrderNo(String taskOrderNo);

    R packageQuery(String orderNo);
    // 派送、揽收任务数据导出
    // 揽收任务数据导出
    List<TmsTransportTaskExcelVo> getTransportTaskExcel(TmsTransportTaskOrderPageVo tmsTransportTaskOrder, Long[] ids);

    // 派送任务数据导出
    List<TmsTransportDeliveryTaskExcelVo> getDeliveryExport(TmsTransportTaskOrderPageVo tmsTransportTaskOrder, Long[] ids);

    Page<TmsCustomerOrderEntity> listDriverDeliveryOrder(Page page, ListDeliveryOrderDTO deliveryOrderDTO);
}
package com.jygjexp.jynx.tms.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.app.api.dto.AppUserInfo;
import com.jygjexp.jynx.app.api.feign.RemoteAppUserService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.constants.TmsMessageTypeConstants;
import com.jygjexp.jynx.tms.dto.TmsCustomerAndTaskDto;
import com.jygjexp.jynx.tms.dto.TmsLmdDriverDTO;
import com.jygjexp.jynx.tms.dto.TmsScheduleAppointDto;
import com.jygjexp.jynx.tms.dto.TmsTransportTaskOrderDto;
import com.jygjexp.jynx.tms.dto.app.ListDeliveryOrderDTO;
import com.jygjexp.jynx.tms.dto.app.TmsAppDriverAndVehicleDto;
import com.jygjexp.jynx.tms.entity.*;
import com.jygjexp.jynx.tms.enums.EntrustedOrderStatus;
import com.jygjexp.jynx.tms.enums.NewOrderStatus;
import com.jygjexp.jynx.tms.enums.TaskType;
import com.jygjexp.jynx.tms.enums.TransportTaskStatus;
import com.jygjexp.jynx.tms.exception.CustomBusinessException;
import com.jygjexp.jynx.tms.mapper.*;
import com.jygjexp.jynx.tms.response.LocalizedR;
import com.jygjexp.jynx.tms.service.*;
import com.jygjexp.jynx.tms.utils.SnowflakeIdGenerator;
import com.jygjexp.jynx.tms.vo.*;
import com.jygjexp.jynx.tms.vo.excel.TmsTransportDeliveryTaskExcelVo;
import com.jygjexp.jynx.tms.vo.excel.TmsTransportTaskExcelVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.swing.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 运输任务单
 *
 * <AUTHOR>
 * @date 2025-04-07 15:31:29
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TmsTransportTaskOrderServiceImpl extends ServiceImpl<TmsTransportTaskOrderMapper, TmsTransportTaskOrderEntity> implements TmsTransportTaskOrderService {
    private final TmsTransportTaskOrderMapper transportTaskOrderMapper;
    private final TmsRoutePlanService routePlanService;
    private final TmsCargoInfoService cargoInfoService;
    private final TmsSiteMapper siteMapper;
    private final TmsLmdDriverMapper driverMapper;
    private final TmsCargoInfoMapper cargoInfoMapper;
    private final TmsOrderTrackService orderTrackService;
    private final TmsCustomerOrderMapper customerOrderMapper;
    private final TmsLineHaulOrderMapper lineHaulOrderMapper;
    private final TmsVehicleInfoMapper vehicleInfoMapper;
    private final TmsVehicleDriverRelationMapper vehicleDriverRelationMapper;
    private final TmsMessageMapper messageMapper;
    private final RemoteAppUserService remoteAppUserService;
    private final TmsCustomerService tmsCustomerService;
    private final TmsDriverAssignHistoryService tmsDriverAssignHistoryService;

    // 自定义雪花 ID 生成器
    private final SnowflakeIdGenerator snowflakeIdGenerator;

    /**
     * 分页查询运输任务单
     *
     * @param page
     * @param vo
     * @return
     */
    @Override
    public Page<TmsTransportTaskOrderPageVo> search(Page page, TmsTransportTaskOrderPageVo vo) {
        MPJLambdaWrapper wrapper = getWrapper(vo, null);
        return transportTaskOrderMapper.selectJoinPage(page, TmsTransportTaskOrderPageVo.class, wrapper);
    }

    /**
     * 查询揽收任务单（全部、待指派、已指派）
     *
     * @param page
     * @param vo
     * @return
     */
    @Override
    public R getAllTmsTransportTaskOrder(Page page, TmsTransportTaskOrderVo vo) {
        MPJLambdaWrapper<TmsTransportTaskOrderEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(TmsTransportTaskOrderEntity.class)
                // 条件查询-客户单号
                .like(StrUtil.isNotBlank(vo.getCustomerOrderNumber()), TmsTransportTaskOrderEntity::getCustomerOrderNumber, vo.getCustomerOrderNumber())
                // 跟踪单号
                .like(StrUtil.isNotBlank(vo.getEntrustedOrderNumber()), TmsTransportTaskOrderEntity::getEntrustedOrderNumber, vo.getEntrustedOrderNumber())
                // 条件查询-任务单号
                .like(StrUtil.isNotBlank(vo.getTaskOrderNo()), TmsTransportTaskOrderEntity::getTaskOrderNo, vo.getTaskOrderNo())
                // 条件查询-任务状态
                .eq(ObjectUtil.isNotNull(vo.getTaskStatus()), TmsTransportTaskOrderEntity::getTaskStatus, vo.getTaskStatus())
                // 条件查询-业务模式
                .eq(ObjectUtil.isNotNull(vo.getBusinessModel()), TmsTransportTaskOrderEntity::getBusinessModel, vo.getBusinessModel())
                // 条件查询-订单类型
                .eq(ObjectUtil.isNotNull(vo.getOrderType()), TmsTransportTaskOrderEntity::getOrderType, vo.getOrderType())
                // 条件查询-揽收任务单
                .eq(TmsTransportTaskOrderEntity::getTaskType, 1)
                // 条件查询-筛选仓库
                .eq(ObjectUtil.isNotNull(vo.getSiteId()), TmsTransportTaskOrderEntity::getSiteId, vo.getSiteId())
                // 条件查询-联系人
                .and(StrUtil.isNotBlank(vo.getLinkman()), w -> w.like(TmsTransportTaskOrderEntity::getShipperName, vo.getLinkman())
                        .or()
                        .like(TmsTransportTaskOrderEntity::getReceiverName, vo.getLinkman()))
                // 条件查询-联系方式（发货人或收货人）
                .and(StrUtil.isNotBlank(vo.getLinkmanPhone()), w -> w.like(TmsTransportTaskOrderEntity::getShipperPhone, vo.getLinkmanPhone())
                        .or()
                        .like(TmsTransportTaskOrderEntity::getReceiverPhone, vo.getLinkmanPhone()))
                //收货方式
                .selectAs(TmsCustomerOrderEntity::getReceiveType, TmsTransportTaskOrderPageVo.Fields.receiveType)
                //客户
                .selectAs(TmsCustomerEntity::getCustomerName, TmsTransportTaskOrderPageVo.Fields.entrustedCustomer)
                // 仓库
                .selectAs(TmsSiteEntity::getSiteName, TmsTransportTaskOrderPageVo.Fields.siteName)
                //主单
                .eq(TmsCustomerOrderEntity::getSubFlag, false)
                //条件查询-委托客户
                .eq(StrUtil.isNotBlank(vo.getEntrustedCustomer()), TmsCustomerEntity::getCustomerName, vo.getEntrustedCustomer())
                // 始发地
                .like(StrUtil.isNotBlank(vo.getOrigin()), TmsTransportTaskOrderEntity::getOrigin, vo.getOrigin())
                // 目的地
                .like(StrUtil.isNotBlank(vo.getDestination()), TmsTransportTaskOrderEntity::getDestination, vo.getDestination())
                .leftJoin(TmsCustomerOrderEntity.class, TmsCustomerOrderEntity::getCustomerOrderNumber, TmsTransportTaskOrderEntity::getCustomerOrderNumber)
                .leftJoin(TmsCustomerEntity.class, TmsCustomerEntity::getId, TmsCustomerOrderEntity::getCustomerId)
                .leftJoin(TmsSiteEntity.class, TmsSiteEntity::getId, TmsTransportTaskOrderEntity::getSiteId)
                // 下单时间 根据范围搜索
                .between(ObjectUtil.isNotNull(vo.getBeginTime()) && ObjectUtil.isNotNull(vo.getEndTime()),
                        TmsTransportTaskOrderEntity::getCreateTime, vo.getBeginTime(), vo.getEndTime())
                // 预计发货时间开始 预计发货时间结束
                .between(ObjectUtil.isNotNull(vo.getEstimatedShippingTimeStart()) && ObjectUtil.isNotNull(vo.getEstimatedShippingTimeEnd()),
                        TmsTransportTaskOrderEntity::getEstimatedShippingTimeStart, vo.getEstimatedShippingTimeStart(), vo.getEstimatedShippingTimeEnd())
                // 预计到货时间开始 预计到货时间结束
                .between(ObjectUtil.isNotNull(vo.getEstimatedArrivalTimeStart()) && ObjectUtil.isNotNull(vo.getEstimatedArrivalTimeEnd()),
                        TmsTransportTaskOrderEntity::getEstimatedArrivalTimeStart, vo.getEstimatedArrivalTimeStart(), vo.getEstimatedArrivalTimeEnd())
                .orderByDesc(TmsTransportTaskOrderEntity::getCreateTime);

        // 打印生成的 SQL 语句进行调试
        String sqlSegment = wrapper.getSqlSegment();
        log.debug("Generated SQL: {}", sqlSegment);

        if (vo.getType().equals(1)) {
            wrapper.isNull(TmsTransportTaskOrderEntity::getDriverId);
        }
        if (vo.getType().equals(2)) {
            wrapper.isNotNull(TmsTransportTaskOrderEntity::getDriverId);
        }

        return R.ok(transportTaskOrderMapper.selectJoinPage(page, TmsTransportTaskOrderPageVo.class, wrapper));
    }


    // 根据id查询揽收任务单详情
    @Override
    public R selectById(Long id) {

        TmsTransportTaskOrderEntity taskOrder = transportTaskOrderMapper.selectById(id);

        MPJLambdaWrapper<TmsTransportTaskOrderEntity> wrapper = new MPJLambdaWrapper<TmsTransportTaskOrderEntity>()
                .selectAll(TmsTransportTaskOrderEntity.class)
                .selectAs(TmsTransportTaskOrderEntity::getCargoQuantity, TmsCustomerAndTaskDto.Fields.totalQuantity)
                .selectAs(TmsLmdDriverEntity::getDriverName, TmsCustomerAndTaskDto.Fields.driverName)
                .selectAs(TmsLmdDriverEntity::getPhone, TmsCustomerAndTaskDto.Fields.phone)
                .selectCount(TmsCustomerOrderEntity::getId, TmsCustomerAndTaskDto.Fields.totalOrderNum)
                .leftJoin(TmsLmdDriverEntity.class, TmsLmdDriverEntity::getDriverId, TmsTransportTaskOrderEntity::getDriverId)
                .eq(TmsTransportTaskOrderEntity::getId, id)
                .groupBy("t.id,t1.driver_name");

        if (taskOrder.getTaskType() == 1) {
            wrapper.leftJoin(TmsCustomerOrderEntity.class, TmsCustomerOrderEntity::getPTaskOrder, TmsTransportTaskOrderEntity::getTaskOrderNo);
        } else {
            wrapper.leftJoin(TmsCustomerOrderEntity.class, TmsCustomerOrderEntity::getDTaskOrder, TmsTransportTaskOrderEntity::getTaskOrderNo);
        }

        TmsCustomerAndTaskDto tmsCustomerAndTaskDto = transportTaskOrderMapper.selectJoinOne(TmsCustomerAndTaskDto.class, wrapper);

        // 根据任务单号和任务类型查询订单列表
        MPJLambdaWrapper<TmsCustomerOrderEntity> orderWrapper = new MPJLambdaWrapper<TmsCustomerOrderEntity>()
                .selectAll(TmsCustomerOrderEntity.class);

        if (taskOrder.getTaskType() == 1) {
            orderWrapper.eq(TmsCustomerOrderEntity::getPTaskOrder, taskOrder.getTaskOrderNo());
        } else {
            orderWrapper.eq(TmsCustomerOrderEntity::getDTaskOrder, taskOrder.getTaskOrderNo());
        }
        orderWrapper.eq(TmsCustomerOrderEntity::getSubFlag, false);
        List<TmsCustomerOrderEntity> orderList = customerOrderMapper.selectJoinList(TmsCustomerOrderEntity.class, orderWrapper);

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("transportTaskOrder", tmsCustomerAndTaskDto);
        jsonObject.put("orderList", orderList);
        return R.ok(jsonObject);
    }

    // 根据id查询订单详情
    @Override
    public R selectByOrderId(Long id, Integer isTask) {
        // 记录任务单仓库
        String sitePName = "";
        String siteDName = "";
        TmsCustomerOrderEntity order = customerOrderMapper.selectById(id);

        // 存储司机id
        List<Long> driverList = new ArrayList<>();
        // 查询揽收任务司机
        if (order.getPTaskOrder() != null) {
            TmsTransportTaskOrderEntity transportPTaskOrder = transportTaskOrderMapper.selectOne(new LambdaQueryWrapper<TmsTransportTaskOrderEntity>()
                    .eq(TmsTransportTaskOrderEntity::getTaskOrderNo, order.getPTaskOrder()), false);
            if (transportPTaskOrder.getDriverId() != null) {
                driverList.add(transportPTaskOrder.getDriverId());
            }
            if (transportPTaskOrder.getSiteId() != null) {
                sitePName = siteMapper.selectById(transportPTaskOrder.getSiteId()).getSiteName();
            }
        }

        // 查询派送任务司机
        if (order.getDTaskOrder() != null) {
            TmsTransportTaskOrderEntity transportDTaskOrder = transportTaskOrderMapper.selectOne(new LambdaQueryWrapper<TmsTransportTaskOrderEntity>()
                    .eq(TmsTransportTaskOrderEntity::getTaskOrderNo, order.getDTaskOrder()), false);
            if (transportDTaskOrder.getDriverId() != null) {
                driverList.add(transportDTaskOrder.getDriverId());
            }
            if (transportDTaskOrder.getSiteId() != null) {
                siteDName = siteMapper.selectById(transportDTaskOrder.getSiteId()).getSiteName();
            }
        }

        // 查询干线任务司机
        if (order.getLineHaulNo() != null) {
            TmsLineHaulOrderEntity lineHaulOrder = lineHaulOrderMapper.selectOne(new LambdaQueryWrapper<TmsLineHaulOrderEntity>()
                    .eq(TmsLineHaulOrderEntity::getLineHaulNo, order.getLineHaulNo()), false);
            if (lineHaulOrder.getDriverId() != null) {
                driverList.add(lineHaulOrder.getDriverId());
            }
        }

        JSONObject jsonObject = new JSONObject();
        // 将driverList去重
        driverList = driverList.stream().distinct().collect(Collectors.toList());

        if (driverList.size() > 0) {
            // 根据司机id查询司机相关信息
            MPJLambdaWrapper<TmsLmdDriverEntity> driverWrapper = new MPJLambdaWrapper<TmsLmdDriverEntity>()
                    .selectAll(TmsLmdDriverEntity.class)
                    .select(TmsVehicleInfoEntity::getLicensePlate)
                    .leftJoin(TmsVehicleDriverRelationEntity.class, TmsVehicleDriverRelationEntity::getDriverId, TmsLmdDriverEntity::getDriverId)
                    .leftJoin(TmsVehicleInfoEntity.class, TmsVehicleInfoEntity::getId, TmsVehicleDriverRelationEntity::getVehicleId)
                    .in(TmsLmdDriverEntity::getDriverId, driverList);

            List<TmsAppDriverAndVehicleDto> tmsLmdDrivers = driverMapper.selectJoinList(TmsAppDriverAndVehicleDto.class, driverWrapper);
            jsonObject.put("driverList", tmsLmdDrivers);

        }

        // 根据客户单号查询货物信息
        MPJLambdaWrapper<TmsCargoInfoEntity> cargoWrapper = new MPJLambdaWrapper<TmsCargoInfoEntity>()
                .selectAll(TmsCargoInfoEntity.class)
                .leftJoin(TmsCustomerOrderEntity.class, TmsCustomerOrderEntity::getCustomerOrderNumber, TmsCargoInfoEntity::getCustomerOrderNumber)
                .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, order.getEntrustedOrderNumber());
        List<TmsCargoInfoEntity> cargoInfoList = cargoInfoMapper.selectJoinList(TmsCargoInfoEntity.class, cargoWrapper);


        if (isTask == 1) {
            jsonObject.put("siteName", sitePName);
        }
        if (isTask == 2) {
            jsonObject.put("siteName", siteDName);
        }
        jsonObject.put("orderList", order);
        jsonObject.put("cargoInfoList", cargoInfoList);
        return R.ok(jsonObject);
    }

    // 构建分页查询条件
    private MPJLambdaWrapper getWrapper(TmsTransportTaskOrderPageVo vo, Long[] ids) {
        MPJLambdaWrapper<TmsTransportTaskOrderEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(TmsTransportTaskOrderEntity.class)
                .like(StrUtil.isNotBlank(vo.getTaskOrderNo()), TmsTransportTaskOrderEntity::getTaskOrderNo, vo.getTaskOrderNo())     // 条件查询-揽收批次
                .eq(ObjectUtil.isNotNull(vo.getTaskStatus()), TmsTransportTaskOrderEntity::getTaskStatus, vo.getTaskStatus())     // 条件查询-任务状态
                .eq(ObjectUtil.isNotNull(vo.getOrderType()), TmsTransportTaskOrderEntity::getOrderType, vo.getOrderType())    // 条件查询-订单类型
                .eq(ObjectUtil.isNotNull(vo.getTaskType()), TmsTransportTaskOrderEntity::getTaskType, vo.getTaskType())     // 条件查询-任务类型  1 揽收；2 派送
                .eq(ObjectUtil.isNotNull(vo.getDriverId()), TmsTransportTaskOrderEntity::getDriverId, vo.getDriverId())     // 条件查询-司机id
                .selectAs(TmsCustomerEntity::getCustomerName, TmsTransportTaskOrderPageVo.Fields.entrustedCustomer)     // 委托客户
                .selectAs(TmsLmdDriverEntity::getDriverName, TmsTransportTaskOrderPageVo.Fields.driverName)    // 司机
                .selectAs(TmsLmdDriverEntity::getPhone, TmsTransportTaskOrderPageVo.Fields.driverPhone)    // 司机电话
                .like(StrUtil.isNotBlank(vo.getEntrustedCustomer()), TmsCustomerEntity::getCustomerName, vo.getEntrustedCustomer())
                .like(StrUtil.isNotBlank(vo.getDriverName()), TmsLmdDriverEntity::getDriverName, vo.getDriverName())
                .like(StrUtil.isNotBlank(vo.getDriverPhone()), TmsLmdDriverEntity::getPhone, vo.getDriverPhone())
                .like(StrUtil.isNotBlank(vo.getContactPhone()), TmsLmdDriverEntity::getPhone, vo.getContactPhone()) // 司机联系方式
                .like(StrUtil.isNotBlank(vo.getOrigin()), TmsTransportTaskOrderEntity::getOrigin, vo.getOrigin())    // 始发地
                .like(StrUtil.isNotBlank(vo.getDestination()), TmsTransportTaskOrderEntity::getDestination, vo.getDestination()) // 目的地
                .leftJoin(TmsCustomerEntity.class, TmsCustomerEntity::getId, TmsTransportTaskOrderEntity::getCustomerId)
                .leftJoin(TmsLmdDriverEntity.class, TmsLmdDriverEntity::getDriverId, TmsTransportTaskOrderEntity::getDriverId)
                .eq(TmsCustomerOrderEntity::getSubFlag, false)
                // 下单时间 根据范围搜索
                .between(ObjectUtil.isNotNull(vo.getBeginTime()) && ObjectUtil.isNotNull(vo.getEndTime()),            // 搜索框 下单时间
                        TmsTransportTaskOrderEntity::getCreateTime, vo.getBeginTime(), vo.getEndTime())
                //预计发货时间开始 预计发货时间结束
                .between(ObjectUtil.isNotNull(vo.getEstimatedShippingTimeStart()) && ObjectUtil.isNotNull(vo.getEstimatedShippingTimeEnd()),
                        TmsTransportTaskOrderEntity::getEstimatedShippingTimeStart, vo.getEstimatedShippingTimeStart(), vo.getEstimatedShippingTimeEnd())
                //预计到货时间开始 预计到货时间结束
                .between(ObjectUtil.isNotNull(vo.getEstimatedArrivalTimeStart()) && ObjectUtil.isNotNull(vo.getEstimatedArrivalTimeEnd()),
                        TmsTransportTaskOrderEntity::getEstimatedArrivalTimeStart, vo.getEstimatedArrivalTimeStart(), vo.getEstimatedArrivalTimeEnd())
                .orderByDesc(TmsTransportTaskOrderEntity::getCreateTime)
                .in(ObjectUtil.isNotEmpty(ids) && ids.length > 0, TmsTransportTaskOrderEntity::getId, ids);

        if (vo.getTaskType() == 1) {
            wrapper.leftJoin(TmsCustomerOrderEntity.class, TmsCustomerOrderEntity::getPTaskOrder, TmsTransportTaskOrderEntity::getTaskOrderNo)
                    .leftJoin(TmsSiteEntity.class, TmsSiteEntity::getId, TmsTransportTaskOrderEntity::getSiteId)
                    .like(StrUtil.isNotBlank(vo.getEntrustedOrderNo()), TmsCustomerOrderEntity::getEntrustedOrderNumber, vo.getEntrustedOrderNo())
                    .eq(ObjectUtil.isNotNull(vo.getOrderTaskStatus()), TmsCustomerOrderEntity::getOrderStatus, vo.getOrderTaskStatus())
                    .eq(ObjectUtil.isNotNull(vo.getBusinessTaskModel()), TmsCustomerOrderEntity::getBusinessModel, vo.getBusinessTaskModel())
                    // 始发地仓库ID
                    .eq(ObjectUtil.isNotNull(vo.getCollectWarehouseId()), TmsCustomerOrderEntity::getCollectWarehouseId, vo.getCollectWarehouseId())
                    .selectAs(TmsSiteEntity::getSiteName, TmsTransportTaskOrderPageVo.Fields.siteName);
//                .selectAs(TmsCustomerOrderEntity::getOrderStatus, TmsTransportTaskOrderPageVo.Fields.orderTaskStatus)
//                .selectAs(TmsCustomerOrderEntity::getBusinessModel, TmsTransportTaskOrderPageVo.Fields.businessTaskModel)
//                .selectAs(TmsCustomerOrderEntity::getEntrustedOrderNumber, TmsTransportTaskOrderPageVo.Fields.entrustedOrderNo)
//                .groupBy("t.id,t1.id,t2.driver_id,t4.id");
            wrapper.groupBy(TmsTransportTaskOrderEntity::getId)
                    .groupBy(TmsCustomerEntity::getId)
                    .groupBy(TmsLmdDriverEntity::getDriverId)
                    .groupBy(TmsSiteEntity::getId);
        } else {
            wrapper.leftJoin(TmsCustomerOrderEntity.class, TmsCustomerOrderEntity::getDTaskOrder, TmsTransportTaskOrderEntity::getTaskOrderNo)
                    .leftJoin(TmsSiteEntity.class, TmsSiteEntity::getId, TmsCustomerOrderEntity::getDeliveryWarehouseId);
            wrapper.leftJoin(TmsVehicleRouteEntity.class, TmsVehicleRouteEntity::getShipmentNo, TmsTransportTaskOrderEntity::getTaskOrderNo);
            wrapper.leftJoin(TmsRoutePlanEntity.class, TmsRoutePlanEntity::getRoutePlanId, TmsVehicleRouteEntity::getRoutePlanId);
            wrapper.leftJoin(TmsPreRoutePlanEntity.class, TmsPreRoutePlanEntity::getId, TmsRoutePlanEntity::getPreRoutePlanId);
            wrapper.like(StrUtil.isNotBlank(vo.getCustomerOrderNumber()), TmsCustomerOrderEntity::getCustomerOrderNumber, vo.getCustomerOrderNumber())
                    .selectAs(TmsPreRoutePlanEntity::getRouteName, TmsTransportTaskOrderPageVo.Fields.batchNo) //运输批次-路径规划
                    .eq(ObjectUtil.isNotNull(vo.getBatchNo()), TmsPreRoutePlanEntity::getRouteName, vo.getBatchNo());
            wrapper.groupBy(TmsTransportTaskOrderEntity::getId)
                    .groupBy(TmsCustomerEntity::getId)
                    .groupBy(TmsPreRoutePlanEntity::getId);
        }

        return wrapper;
    }

    /**
     * 创建运输任务单
     *
     * @param order
     * @return
     */
    @Override
    public R createTransportTaskOrder(TmsCustomerOrderEntity order) {
        //1.送货到仓，先新增干线任务，再生成派送任务单
        //2.上门揽收，生成揽收任务，送货到一级仓之后，新增干线任务单，判断一级仓是否可派送，若可派送，直接生成派送任务单，若不可派送，则新增干线任务单，再生成派送任务单
        try {
            log.info("开始生成任务单，跟踪单号：{}", order.getEntrustedOrderNumber());

            // 1. 参数校验
            R validationResult = validateOrder(order);
            if (!validationResult.isOk()) {
                return validationResult;
            }

            // 2. 查询货物信息
            List<TmsCargoInfoEntity> cargoInfos = getCargoInfos(order);
            if (cargoInfos == null || cargoInfos.isEmpty()) {
                return LocalizedR.failed("tms.cargo.info.empty", "");
            }

            // 3. 根据收货方式创建任务单
//            if (order.getReceiveType() == 1) {
            // 上门揽收：创建揽收任务单和派送任务单
            // 统一返回结果
            Map<String, String> taskResult = new HashMap<>();
            R pickupResult = createPickupTask(order, cargoInfos);
            if (!pickupResult.isOk()) {
                return pickupResult;
            }
            taskResult.put("pickupTaskNo", (String) pickupResult.getData());

            R deliveryResult = createDeliveryTask(order, cargoInfos);
            if (!deliveryResult.isOk()) {
                return deliveryResult;
            }
            taskResult.put("deliveryTaskNo", (String) deliveryResult.getData());
//            } else {
//                // 送货到仓：只创建派送任务单
//                R deliveryResult = createDeliveryTask(order, cargoInfos);
//                if (!deliveryResult.isOk()) {
//                    return deliveryResult;
//                }
//            }

            return LocalizedR.ok(taskResult);
        } catch (Exception e) {
            log.error("任务单生成失败，订单号：{}", order.getEntrustedOrderNumber(), e);
            return LocalizedR.failed("tms.failed.to.create.transport.task.order", "");
        }
    }

    /**
     * 多客户单生成揽收任务单
     *
     * @param orderList
     * @param driverId
     * @return
     */
    @Override
    public List<Long> createPickupTaskOrder(List<TmsCustomerOrderEntity> orderList, Long driverId) {
        List<Long> createdTaskIds = new ArrayList<>();
        try {
            // 1.根据司机id查询车辆信息
            TmsVehicleDriverRelationEntity tmsVehicleDriverRelation = vehicleDriverRelationMapper.selectOne(new LambdaQueryWrapper<TmsVehicleDriverRelationEntity>()
                    .eq(TmsVehicleDriverRelationEntity::getDriverId, driverId), false);
            TmsVehicleInfoEntity vehicleInfo = vehicleInfoMapper.selectOne(new LambdaQueryWrapper<TmsVehicleInfoEntity>()
                    .eq(TmsVehicleInfoEntity::getId, tmsVehicleDriverRelation.getVehicleId()));
            // 查询司机信息
            TmsLmdDriverEntity driver = driverMapper.selectOne(new LambdaQueryWrapper<TmsLmdDriverEntity>()
                    .eq(TmsLmdDriverEntity::getDriverId, driverId), false);

            for (TmsCustomerOrderEntity order : orderList) {
                TmsTransportTaskOrderEntity taskOrder = new TmsTransportTaskOrderEntity();
//            taskOrder.setId(null);
                // 2. 设置揽收任务特有属性
                taskOrder.setTaskType(1); // 1: 揽收任务
                String taskOrderNo = generatePTaskOrderNo("S");
                taskOrder.setTaskOrderNo(taskOrderNo); // S: 揽收

                // 设置任务状态
                taskOrder.setTaskStatus(TransportTaskStatus.PENDING_PICKUP.getCode());
                taskOrder.setCreateTime(LocalDateTime.now());
                // 司机和车辆信息
                taskOrder.setDriverId(driverId);
                taskOrder.setLicensePlate(vehicleInfo.getLicensePlate());
                // 设置仓库id  多个订单，但是他们的仓库ID是同一个  取第一单的仓库ID
                taskOrder.setSiteId(orderList.get(0).getCollectWarehouseId());

                List<TmsCustomerOrderEntity> orderLists = new ArrayList<>();
                orderLists.add(order);
                // 3. 汇总总体积、数量、重量
                SummaryResultVo summaryResultVo = calculateSummary(orderLists);
                taskOrder.setTotalVolume(summaryResultVo.getTotalVolume());
                taskOrder.setTotalWeight(summaryResultVo.getTotalWeight());
                taskOrder.setCargoQuantity(summaryResultVo.getTotalQuantity());
                // 记录司机揽收情况
                TmsDriverAssignHistoryEntity tmsDriverAssignHistory = new TmsDriverAssignHistoryEntity();
                tmsDriverAssignHistory.setDriverId(driverId);
                tmsDriverAssignHistory.setDriverName(driver.getDriverName());
                tmsDriverAssignHistory.setDriverNum(driver.getDriverNum());
                tmsDriverAssignHistory.setOrderNo(order.getEntrustedOrderNumber());
                tmsDriverAssignHistory.setAssignType(TaskType.COLLECTION.getCode());
                tmsDriverAssignHistory.setDescription("揽收任务");

                tmsDriverAssignHistoryService.save(tmsDriverAssignHistory);

                // 4. 保存揽收任务单
                transportTaskOrderMapper.insert(taskOrder);
                createdTaskIds.add(taskOrder.getId());

                // 订单绑定揽收任务单号
                customerOrderMapper.update(new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
                        .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, order.getEntrustedOrderNumber())   // 多个跟踪单号
                        .set(TmsCustomerOrderEntity::getPTaskOrder, taskOrderNo)
                );
                log.info("订单 {} 创建揽收任务单成功，任务单号：{}", order.getEntrustedOrderNumber(), taskOrderNo);
            }

            return createdTaskIds;
        } catch (Exception e) {
            log.error("创建揽收任务单失败", e);
            return null;
        }
    }

    // 获取司机
    @Override
    public List<TmsLmdDriverPageVo> getDriverByOrderNo(String orderNo) {
        MPJLambdaWrapper<TmsTransportTaskOrderEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.select(TmsLmdDriverEntity::getDriverName, TmsLmdDriverEntity::getPhone)
                .select(TmsVehicleInfoEntity::getLicensePlate)
                .eq(TmsTransportTaskOrderEntity::getTaskOrderNo, orderNo)
                .leftJoin(TmsVehicleDriverRelationEntity.class, TmsVehicleDriverRelationEntity::getDriverId, TmsTransportTaskOrderEntity::getDriverId)
                .leftJoin(TmsVehicleInfoEntity.class, TmsVehicleInfoEntity::getId, TmsVehicleDriverRelationEntity::getVehicleId)
                .leftJoin(TmsLmdDriverEntity.class, TmsLmdDriverEntity::getDriverId, TmsTransportTaskOrderEntity::getDriverId);
        return transportTaskOrderMapper.selectJoinList(TmsLmdDriverPageVo.class, wrapper);

    }

    /**
     * 根据客户单号查询订单详情
     */
    @Override
    public R getCustomerOrderByCustomerOrderNumber(String customerOrderNumber, String siteId) {
        //查询客户订单
        TmsCustomerOrderEntity customerOrder = customerOrderMapper.selectOne(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                .eq(TmsCustomerOrderEntity::getCustomerOrderNumber, customerOrderNumber)
                .eq(TmsCustomerOrderEntity::getSubFlag, 0));
        //查询客户
        TmsCustomerEntity customer = tmsCustomerService.getById(customerOrder.getCustomerId());
        //根据客户单号查询揽收任务单
        TmsTransportTaskOrderEntity tmsTransportTaskOrderEntity = this.getOne(new LambdaQueryWrapper<TmsTransportTaskOrderEntity>()
                .eq(TmsTransportTaskOrderEntity::getCustomerOrderNumber, customerOrderNumber).eq(TmsTransportTaskOrderEntity::getTaskType, 1));
        //查询仓库信息
        TmsSiteEntity siteInfo = siteMapper.selectOne(new LambdaQueryWrapper<TmsSiteEntity>()
                .eq(TmsSiteEntity::getId, siteId));
        //查询货物信息
        List<TmsCargoInfoEntity> cargoInfoEntityList = cargoInfoService.list(new LambdaQueryWrapper<TmsCargoInfoEntity>()
                .eq(TmsCargoInfoEntity::getCustomerOrderNumber, customerOrderNumber));
        //回填返回的Vo数据
        if (ObjectUtil.isNotNull(cargoInfoEntityList) && !cargoInfoEntityList.isEmpty()) {
            CustomerOrderDetailVo customerOrderDetailVo = new CustomerOrderDetailVo();
            SummaryResultVo summaryResultVo = cargoInfoService.calculateSummary(cargoInfoEntityList);
            customerOrderDetailVo.setCustomerOrder(customerOrder);
            customerOrderDetailVo.setWarehouseName(siteInfo.getSiteName());
            customerOrderDetailVo.setCargoInfoList(cargoInfoEntityList);
            customerOrderDetailVo.setSummaryResultVo(summaryResultVo);
            customerOrderDetailVo.setCollectOrderNumber(tmsTransportTaskOrderEntity.getTaskOrderNo());
            customerOrderDetailVo.setCollectTaskGenerationTime(tmsTransportTaskOrderEntity.getCreateTime());
            //司机id用与判断是否指派
            customerOrderDetailVo.setDriverId(tmsTransportTaskOrderEntity.getDriverId());
            if (ObjectUtil.isNotNull(customer)) {
                customerOrderDetailVo.setCustomer(customer.getCustomerName());
            }
            return R.ok(customerOrderDetailVo);
        }
        return R.failed("没有找到相关订单信息！");
    }

    /**
     * 根据任务类型查询已完成的揽收任务或者派送任务
     */
    @Override
    public R getFinishCollectOrDeliveryTask(Page page, Integer taskType) {
        List<TmsLargeTaskTrailVo> tmsLargeTaskTrailVos = new ArrayList<>();
        if (taskType.equals(1)) {
            //查询已完成的揽收任务
            LambdaQueryWrapper<TmsTransportTaskOrderEntity> wrapper = new LambdaQueryWrapper<TmsTransportTaskOrderEntity>()
                    .eq(TmsTransportTaskOrderEntity::getTaskType, TaskType.COLLECTION.getCode())
                    .eq(TmsTransportTaskOrderEntity::getTaskStatus, TransportTaskStatus.DELIVERED.getCode())
                    .orderByDesc(TmsTransportTaskOrderEntity::getCreateTime);
            Page newPage = transportTaskOrderMapper.selectPage(page, wrapper);
            List<TmsTransportTaskOrderEntity> records = newPage.getRecords();
            if (ObjectUtil.isNotNull(records) && !records.isEmpty()) {
                //司机信息
                List<Long> driverIds = records.stream()
                        .map(TmsTransportTaskOrderEntity::getDriverId).collect(Collectors.toList());
                Map<Long, TmsLmdDriverEntity> driverMap = driverMapper.selectList(new LambdaQueryWrapper<TmsLmdDriverEntity>()
                                .in(TmsLmdDriverEntity::getDriverId, driverIds))
                        .stream().collect(Collectors.toMap(TmsLmdDriverEntity::getDriverId, Function.identity()));
                //车辆信息
                Map<Long, TmsVehicleInfoEntity> vehicleMap = vehicleInfoMapper.selectList(new LambdaQueryWrapper<TmsVehicleInfoEntity>()
                                .in(TmsVehicleInfoEntity::getDriverId, driverIds))
                        .stream().collect(Collectors.toMap(TmsVehicleInfoEntity::getDriverId, Function.identity()));
                //遍历回填数据
                records.forEach(task -> {
                    TmsLargeTaskTrailVo tmsLargeTaskTrailVo = new TmsLargeTaskTrailVo();
                    tmsLargeTaskTrailVo.setTaskOrder(task);
                    tmsLargeTaskTrailVo.setTaskType(taskType);
                    //司机信息
                    if (ObjectUtil.isNotNull(driverMap.get(task.getDriverId()))) {
                        tmsLargeTaskTrailVo.setDriverName(driverMap.get(task.getDriverId()).getDriverName());
                    }
                    //车牌
                    if (ObjectUtil.isNotNull(vehicleMap.get(task.getDriverId()))) {
                        tmsLargeTaskTrailVo.setLicensePlate(vehicleMap.get(task.getDriverId()).getLicensePlate());
                    }
                    tmsLargeTaskTrailVos.add(tmsLargeTaskTrailVo);

                });
            }
            return R.ok(newPage.setRecords(tmsLargeTaskTrailVos));
        } else if (taskType.equals(2)) {
            //查询已完成的派送任务
            LambdaQueryWrapper<TmsTransportTaskOrderEntity> wrapper = new LambdaQueryWrapper<TmsTransportTaskOrderEntity>()
                    .eq(TmsTransportTaskOrderEntity::getTaskType, TaskType.DELIVERY.getCode())
                    .eq(TmsTransportTaskOrderEntity::getTaskStatus, TransportTaskStatus.DELIVERED.getCode())
                    .orderByDesc(TmsTransportTaskOrderEntity::getCreateTime);
            Page newPage = transportTaskOrderMapper.selectPage(page, wrapper);
            List<TmsTransportTaskOrderEntity> records = newPage.getRecords();
            if (ObjectUtil.isNotNull(records) && !records.isEmpty()) {
                //司机信息
                List<Long> driverIds = records.stream()
                        .map(TmsTransportTaskOrderEntity::getDriverId).collect(Collectors.toList());
                Map<Long, TmsLmdDriverEntity> driverMap = driverMapper.selectList(new LambdaQueryWrapper<TmsLmdDriverEntity>()
                                .in(TmsLmdDriverEntity::getDriverId, driverIds))
                        .stream().collect(Collectors.toMap(TmsLmdDriverEntity::getDriverId, Function.identity()));
                //车辆信息
                Map<Long, TmsVehicleInfoEntity> vehicleMap = vehicleInfoMapper.selectList(new LambdaQueryWrapper<TmsVehicleInfoEntity>()
                                .in(TmsVehicleInfoEntity::getDriverId, driverIds))
                        .stream().collect(Collectors.toMap(TmsVehicleInfoEntity::getDriverId, Function.identity()));
                //遍历回填数据
                records.forEach(task -> {
                    TmsLargeTaskTrailVo tmsLargeTaskTrailVo = new TmsLargeTaskTrailVo();
                    tmsLargeTaskTrailVo.setTaskOrder(task);
                    tmsLargeTaskTrailVo.setTaskType(taskType);
                    //司机信息
                    if (ObjectUtil.isNotNull(driverMap.get(task.getDriverId()))) {
                        tmsLargeTaskTrailVo.setDriverName(driverMap.get(task.getDriverId()).getDriverName());
                    }
                    //车牌
                    if (ObjectUtil.isNotNull(vehicleMap.get(task.getDriverId()))) {
                        tmsLargeTaskTrailVo.setLicensePlate(vehicleMap.get(task.getDriverId()).getLicensePlate());
                    }
                    tmsLargeTaskTrailVos.add(tmsLargeTaskTrailVo);

                });
            }
            return R.ok(newPage.setRecords(tmsLargeTaskTrailVos));
        } else {
            //查询已完成的揽收任务
            LambdaQueryWrapper<TmsLineHaulOrderEntity> wrapper = new LambdaQueryWrapper<TmsLineHaulOrderEntity>()
                    .eq(TmsLineHaulOrderEntity::getTaskStatus, TransportTaskStatus.DELIVERED.getCode())
                    .orderByDesc(TmsLineHaulOrderEntity::getCreateTime);
            Page newPage = lineHaulOrderMapper.selectPage(page, wrapper);
            List<TmsLineHaulOrderEntity> records = newPage.getRecords();
            if (ObjectUtil.isNotNull(records) && !records.isEmpty()) {
                //司机信息
                List<Long> driverIds = records.stream()
                        .map(TmsLineHaulOrderEntity::getDriverId).collect(Collectors.toList());
                Map<Long, TmsLmdDriverEntity> driverMap = driverMapper.selectList(new LambdaQueryWrapper<TmsLmdDriverEntity>()
                                .in(TmsLmdDriverEntity::getDriverId, driverIds))
                        .stream().collect(Collectors.toMap(TmsLmdDriverEntity::getDriverId, Function.identity()));
                //车辆信息
                Map<Long, TmsVehicleInfoEntity> vehicleMap = vehicleInfoMapper.selectList(new LambdaQueryWrapper<TmsVehicleInfoEntity>()
                                .in(TmsVehicleInfoEntity::getDriverId, driverIds))
                        .stream().collect(Collectors.toMap(TmsVehicleInfoEntity::getDriverId, Function.identity()));
                //遍历回填数据
                records.forEach(task -> {
                    TmsLargeTaskTrailVo tmsLargeTaskTrailVo = new TmsLargeTaskTrailVo();
                    tmsLargeTaskTrailVo.setLineHaulOrder(task);
                    //干线标志
                    tmsLargeTaskTrailVo.setTaskType(TaskType.DRYLINE.getCode());
                    //司机信息
                    if (ObjectUtil.isNotNull(driverMap.get(task.getDriverId()))) {
                        tmsLargeTaskTrailVo.setDriverName(driverMap.get(task.getDriverId()).getDriverName());
                    }
                    //车牌
                    if (ObjectUtil.isNotNull(vehicleMap.get(task.getDriverId()))) {
                        tmsLargeTaskTrailVo.setLicensePlate(vehicleMap.get(task.getDriverId()).getLicensePlate());
                    }
                    tmsLargeTaskTrailVos.add(tmsLargeTaskTrailVo);

                });
            }
            return R.ok(newPage.setRecords(tmsLargeTaskTrailVos));
        }
    }

    /**
     * 汇总总体积、数量、重量
     */
    private SummaryResultVo calculateSummary(List<TmsCustomerOrderEntity> customerList) {
        BigDecimal totalWeight = BigDecimal.ZERO;
        BigDecimal totalVolume = BigDecimal.ZERO;
        Integer totalQuantity = 0;
        for (TmsCustomerOrderEntity customerOrder : customerList) {
            // 直接累加货物的重量
            totalWeight = totalWeight.add(customerOrder.getTotalWeight());
            // 直接累加货物的体积
            totalVolume = totalVolume.add(customerOrder.getTotalVolume());
            // 直接累加货物的数量
            totalQuantity = totalQuantity + customerOrder.getCargoQuantity();
        }
        SummaryResultVo result = new SummaryResultVo();
        result.setTotalWeight(totalWeight);
        result.setTotalVolume(totalVolume);
        result.setTotalQuantity(totalQuantity);

        return result;
    }

    // 创建揽收任务单(旧)
    private R createPickupTask(TmsCustomerOrderEntity order, List<TmsCargoInfoEntity> cargoInfos) {
        try {
            TmsTransportTaskOrderEntity task = buildBaseTask(order, cargoInfos);

            // 设置揽收任务特有属性
            task.setTaskType(1); // 1: 揽收任务
            task.setTaskOrderNo(generateTaskOrderNo(order, "S")); // S: 揽收
            task.setTaskStatus(TransportTaskStatus.PENDING_PICKUP.getCode());

            // 获取揽收仓库
            R warehouseResult = getWarehouseId(order, true);
            if (!warehouseResult.isOk()) {
                return warehouseResult;
            }
            task.setSiteId((Long) warehouseResult.getData());

            transportTaskOrderMapper.insert(task);
            log.info("揽收任务单创建成功，单号：{}", task.getTaskOrderNo());

            // 将该任务单设置到客户订单中
            LambdaUpdateWrapper<TmsCustomerOrderEntity> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(TmsCustomerOrderEntity::getPTaskOrder, task.getTaskOrderNo())
                    .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, order.getEntrustedOrderNumber());
            customerOrderMapper.update(updateWrapper);

            return LocalizedR.ok(task.getTaskOrderNo());
        } catch (Exception e) {
            log.error("创建揽收任务单失败", e);
            return LocalizedR.failed("tms.pickup.task.created.failed", "");
        }
    }

    // 创建派送任务单(旧)
    private R createDeliveryTask(TmsCustomerOrderEntity order, List<TmsCargoInfoEntity> cargoInfos) {
        try {
            TmsTransportTaskOrderEntity task = buildBaseTask(order, cargoInfos);

            // 设置派送任务特有属性
            task.setTaskType(2); // 2: 派送任务
            task.setTaskOrderNo(generateTaskOrderNo(order, "D")); // D: 派送
            task.setTaskStatus(TransportTaskStatus.PENDING_PICKUP.getCode()); // 派送任务也使用待提货状态

            // 获取派送仓库
            R warehouseResult = getWarehouseId(order, false);
            if (!warehouseResult.isOk()) {
                return warehouseResult;
            }
            task.setSiteId((Long) warehouseResult.getData());

            transportTaskOrderMapper.insert(task);
            log.info("派送任务单创建成功，单号：{}", task.getTaskOrderNo());

            // 将该任务单设置到客户订单中
            LambdaUpdateWrapper<TmsCustomerOrderEntity> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(TmsCustomerOrderEntity::getDTaskOrder, task.getTaskOrderNo())
                    .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, order.getEntrustedOrderNumber());
            customerOrderMapper.update(updateWrapper);

            // 保存轨迹 先查询出客户主订单及子单 主单及子单都生成一条轨迹
//            String entrustedOrderNumber = order.getEntrustedOrderNumber();
//            String mainOrderNo = entrustedOrderNumber.substring(0, entrustedOrderNumber.length() - 3);
//            List<TmsCustomerOrderEntity> allOrders = customerOrderMapper.selectList(Wrappers.lambdaQuery(TmsCustomerOrderEntity.class)
//                    .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, mainOrderNo));
//            List<String> orderNos = allOrders.stream().map(TmsCustomerOrderEntity::getEntrustedOrderNumber).collect(Collectors.toList());
//            for (String orderNo : orderNos) {
//                (orderNo, order.getCustomerOrderNumber(),
//                        NewOrderStatus.AWAITING_DELIVERY.getValue(), "", "Awaiting Receipt - Generate Delivery Task","", 1);
//            }

            return LocalizedR.ok(task.getTaskOrderNo());
        } catch (Exception e) {
            log.error("创建派送任务单失败", e);
            return LocalizedR.failed("tms.delivery.task.created.failed", "");
        }
    }

    // 任务单基础信息
    private TmsTransportTaskOrderEntity buildBaseTask(TmsCustomerOrderEntity order, List<TmsCargoInfoEntity> cargoInfos) {
        TmsTransportTaskOrderEntity task = new TmsTransportTaskOrderEntity();
        BeanUtils.copyProperties(order, task);
        task.setId(null);
        // 此时是子单号，需要截取掉尾部的3位
        String entrustedOrderNumber = order.getEntrustedOrderNumber();
        String mainOrderNo = entrustedOrderNumber.substring(0, entrustedOrderNumber.length() - 3);
        task.setEntrustedOrderNumber(mainOrderNo);
        task.setCreateTime(LocalDateTime.now());

        // 设置地址信息
        task.setShipperLatLng(getLatLngByAddress(order.getShipperAddress(), order.getOrigin(), order.getShipperPostalCode()));
        task.setReceiverLatLng(getLatLngByAddress(order.getDestAddress(), order.getDestination(), order.getDestPostalCode()));
        return task;
    }

    // 通过地址获取经纬度
    private String getLatLngByAddress(String address, String region, String postalCode) {
        String fullAddress = buildFullAddress(address, region, postalCode);
        return routePlanService.getLatLngByAddress(fullAddress);
    }

    // 构建完整地址
    private String buildFullAddress(String address, String location, String postalCode) {
        if (location.contains("Canada")) {
            String[] parts = location.split("/");
            String country = parts[0];
            String state = parts[1];
            String city = parts[2];
            postalCode = postalCode.replace(" ", "");
            return address + ", " + city + ", " + state + ", " + postalCode + ", " + country;
        } else {
            return address;
        }
    }

    /**
     * 获取仓库ID
     *
     * @param isPickup 是否为揽收任务
     */
    private R getWarehouseId(TmsCustomerOrderEntity order, boolean isPickup) {
        try {
            String postalCode = isPickup ? order.getShipperPostalCode() : order.getDestPostalCode();
            postalCode = postalCode.trim().substring(0, 3);

            MPJLambdaWrapper<TmsSiteEntity> wrapper = new MPJLambdaWrapper<>();
            wrapper.like(TmsOverAreaEntity::getZip, postalCode)
                    .selectAll(TmsSiteEntity.class)
                    .leftJoin(TmsOverAreaEntity.class, TmsOverAreaEntity::getWarehouseId, TmsSiteEntity::getId);

            TmsSiteEntity site = siteMapper.selectOne(wrapper, false);
            if (null == site) {
                log.warn("找不到对应的仓库，邮编: {}", postalCode);
                return LocalizedR.failed("tms.site.empty", postalCode);
            }
            return R.ok(site.getId());
        } catch (Exception e) {
            log.error("获取仓库ID失败", e);
            return LocalizedR.failed("tms.get.warehouse.failed", "");
        }
    }

    // 参数校验
    private R validateOrder(TmsCustomerOrderEntity order) {
        if (order.getReceiveType() == null || (order.getReceiveType() != 1 && order.getReceiveType() != 2)) {
            return LocalizedR.failed("tms.invalid.receive.type", "");
        }
        if (order.getCustomerId() == null) {
            return LocalizedR.failed("tms.customer.id.empty", "");
        }
        return R.ok();
    }

    // 查询货物信息
    private List<TmsCargoInfoEntity> getCargoInfos(TmsCustomerOrderEntity order) {
        LambdaQueryWrapper<TmsCargoInfoEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TmsCargoInfoEntity::getCustomerOrderNumber, order.getCustomerOrderNumber());
        return cargoInfoService.list(wrapper);
    }

    /**
     * 生成符合规则的任务单号 格式：[租户标识]-[单据类型]-[日期时间戳]-[序列号]
     */
    private String generateTaskOrderNo(TmsCustomerOrderEntity order, String taskTypeCode) {
        // 获取客户ID（格式：N + 客户ID）
        String customerCode = "N" + order.getCustomerId();
        String datePart = LocalDate.now().format(DateTimeFormatter.ofPattern("yyMMdd"));    // 250330,250331
        long snowflakeId = snowflakeIdGenerator.nextId();
        String snowflakeIdStr = String.valueOf(snowflakeId);
        String shortId = snowflakeIdStr.substring(snowflakeIdStr.length() - 6); // 取后 6 位
        // 组合单号
        return String.format("%s%s%s%s", customerCode, taskTypeCode, datePart, shortId); // N1S250330645213
    }

    /**
     * 生成揽收任务单号 格式：[N]-[单据类型]-[日期时间戳]-[序列号]
     */
    private String generatePTaskOrderNo(String taskTypeCode) {
        // 获取客户ID（格式：N + 客户ID）
        String customerCode = "N";
        String datePart = LocalDate.now().format(DateTimeFormatter.ofPattern("yyMMdd"));    // 250330,250331
        long snowflakeId = snowflakeIdGenerator.nextId();
        String snowflakeIdStr = String.valueOf(snowflakeId);
        String shortId = snowflakeIdStr.substring(snowflakeIdStr.length() - 6); // 取后6位
        // 组合单号
        return String.format("%s%s%s%s", customerCode, taskTypeCode, datePart, shortId); // NS250330645213
    }

    /**
     * 揽收指派
     *
     * @param appointDto
     * @return
     */
    @Override
    public R collectAppointNew(TmsScheduleAppointDto appointDto) {
        // 1.查询待指派的客户订单信息
        List<TmsCustomerOrderEntity> orderList = customerOrderMapper.selectList(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, appointDto.getEntrustedOrderNoList())         // 多个跟踪单号
        );

        if (orderList.isEmpty()) {
            return LocalizedR.failed("tms.not.exist.task.order", "");
        }
        // 判断对应订单状态是否已经变更
        List<String> invalidAppointOrder = orderList.stream()
                .filter(order -> !order.getOrderStatus().equals(NewOrderStatus.AWAITING_PICKUP.getCode()))
                .map(TmsCustomerOrderEntity::getEntrustedOrderNumber)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(invalidAppointOrder)) {
            return LocalizedR.failed("tms.order.status.already.changed", String.join(",", invalidAppointOrder));
        }

        // 1.1 判断这些订单下是否已经指分配过
        List<String> customerNos = orderList.stream().map(TmsCustomerOrderEntity::getPTaskOrder).collect(Collectors.toList());
        Long count = customerOrderMapper.selectCount(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                .in(TmsCustomerOrderEntity::getPTaskOrder, customerNos)
                .isNotNull(TmsCustomerOrderEntity::getPTaskOrder));
        if (count > 0) {
            return LocalizedR.failed("tms.order.already.assigned", "");
        }

        // 1.2 筛选多个订单的始发地仓库ID必须是同一个，才能指派司机
        Set<Long> siteIds = orderList.stream().map(order -> order.getCollectWarehouseId()).filter(Objects::nonNull).collect(Collectors.toSet());
        if (siteIds.size() > 1) {
            return LocalizedR.failed("tms.order.site.not.same", "");
        }

        // 1.3 拦截 receiveType 为 2（送货到仓）的订单
        List<TmsCustomerOrderEntity> invalidReceiveTypeOrders = orderList.stream()
                .filter(order -> Objects.equals(order.getReceiveType(), 2))
                .collect(Collectors.toList());

        if (!invalidReceiveTypeOrders.isEmpty()) {
            String invalidOrderNos = invalidReceiveTypeOrders.stream()
                    .map(TmsCustomerOrderEntity::getEntrustedOrderNumber)
                    .collect(Collectors.joining(","));
            return LocalizedR.failed("tms.receive.type.not.support", invalidOrderNos);
        }

        // 2. 多客户单生成揽收任务单，并指派司机
        // 先查询司机是否开工
        TmsLmdDriverEntity driverEntity = driverMapper.selectOne(new LambdaQueryWrapper<TmsLmdDriverEntity>()
                .eq(TmsLmdDriverEntity::getDriverId, appointDto.getDriverId()), false);

        TmsVehicleDriverRelationEntity tmsVehicleDriverRelationEntity = vehicleDriverRelationMapper.selectOne(new LambdaQueryWrapper<TmsVehicleDriverRelationEntity>()
                .eq(TmsVehicleDriverRelationEntity::getDriverId, appointDto.getDriverId()).last("limit 1"));
        if (ObjectUtil.isNull(tmsVehicleDriverRelationEntity)) {
            return LocalizedR.failed("tms.driver.no.vehicle", appointDto.getDriverId());
        }

        // 4. 指派生成任务操作
        List<Long> taskOrderIds = createPickupTaskOrder(orderList, appointDto.getDriverId());
        String taskOrderIdStr = String.valueOf(taskOrderIds);

        List<TmsTransportTaskOrderEntity> taskOrders = transportTaskOrderMapper.selectBatchIds(taskOrderIds);

        // 5. 指派成功，发送站内信息给司机
        // 处理成功之后会发送站内信消息通知给司机
        R<AppUserInfo> userInfo = null;
        if (ObjectUtil.isNotNull(driverEntity)) {
            userInfo = remoteAppUserService.info(driverEntity.getPhone());
            Long userId = userInfo.getData().getAppUser().getUserId();

            for (TmsTransportTaskOrderEntity taskOrder : taskOrders) {
                // 查询该任务单下对应的订单号
//                LambdaQueryWrapper<TmsCustomerOrderEntity> customerOrderWrapper = new LambdaQueryWrapper<TmsCustomerOrderEntity>()
//                        .eq(TmsCustomerOrderEntity::getPTaskOrder, taskOrder.getTaskOrderNo())
//                        .eq(TmsCustomerOrderEntity::getSubFlag, Boolean.FALSE);
//                TmsCustomerOrderEntity customerOrders = customerOrderMapper.selectOne(customerOrderWrapper);

                TmsMessageEntity message = new TmsMessageEntity();
                message.setUserId(userId);
                message.setOrderNo(taskOrder.getTaskOrderNo());
                message.setMessage("You have a new collect task, please check!");
                message.setMessageType(TmsMessageTypeConstants.PICKUP_ORDER);
                // 新增消息记录
                messageMapper.insert(message);
            }
        }
        return R.ok(taskOrderIdStr, "The collection task assignment succeeded!");
    }

    /**
     * 根据派送任务批次号查询该任务中的所有订单
     *
     * @param taskOrderNo
     * @return
     */
    @Override
    public R getDriverDeliveryOrderByTaskOrderNo(String taskOrderNo) {
        //查询指派给司机的任务单
        TmsTransportTaskOrderEntity tmsTransportTaskOrderEntity = this.getOne(new LambdaQueryWrapper<TmsTransportTaskOrderEntity>()
                .eq(TmsTransportTaskOrderEntity::getTaskOrderNo, taskOrderNo), false);
        if (ObjectUtil.isNull(tmsTransportTaskOrderEntity)) {
            return R.failed(50007, LocalizedR.getMessage("route_planning.task_order_not_exists", null));
        }
        //派送任务处理
        List<TmsCustomerOrderEntity> tmsCustomerOrders = customerOrderMapper.selectList(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                .eq(TmsCustomerOrderEntity::getDTaskOrder, taskOrderNo));
        return R.ok(tmsCustomerOrders);
    }

    //  揽收任务数据导出
    @Override
    public R packageQuery(String orderNo) {
        String masterOrderNo = orderNo;
        //校验订单-N开头-位数大于12（主单13位，子单16位）
        if (!orderNo.matches("^N[0-9]{12,16}$")) {
            throw new CustomBusinessException("订单号格式错误,请检查！");
        }
        //如果是子单，截取后三位得到主单号
        if (orderNo.length() > 13) {
            masterOrderNo = orderNo.substring(0, orderNo.length() - 3);
        }
        //根据主单查询
        TmsCustomerOrderEntity customerOrder = customerOrderMapper.selectOne(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, masterOrderNo));
        //根据主单的状态
        if (customerOrder.getOrderStatus() < NewOrderStatus.AWAITING_TRANSPORTATION.getCode()) {
            //订单状态小于待运输，则说明此时是待揽收

        }

        return null;
    }

    //  派送、揽收任务数据导出
    @Override
    public List<TmsTransportTaskExcelVo> getTransportTaskExcel(TmsTransportTaskOrderPageVo tmsTransportTaskOrder, Long[] ids) {
        MPJLambdaWrapper wrapper = getWrapper(tmsTransportTaskOrder, ids);
        return transportTaskOrderMapper.selectJoinList(TmsTransportTaskExcelVo.class, wrapper);
    }

    //  派送任务数据导出
    @Override
    public List<TmsTransportDeliveryTaskExcelVo> getDeliveryExport(TmsTransportTaskOrderPageVo tmsTransportTaskOrder, Long[] ids) {
        MPJLambdaWrapper wrapper = getWrapper(tmsTransportTaskOrder, ids);
        return transportTaskOrderMapper.selectJoinList(TmsTransportDeliveryTaskExcelVo.class, wrapper);
    }

    @Override
    public Page<TmsCustomerOrderEntity> listDriverDeliveryOrder(Page page, ListDeliveryOrderDTO deliveryOrderDTO) {
        if (ObjectUtils.isEmpty(deliveryOrderDTO.getDriverId())) {
            throw new RuntimeException("司机id为空");
        }
        MPJLambdaWrapper<TmsCustomerOrderEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(TmsCustomerOrderEntity.class)
                .eq(TmsCustomerOrderEntity::getDeliveryDriverId, deliveryOrderDTO.getDriverId())
                .eq(TmsCustomerOrderEntity::getSubFlag, Boolean.FALSE)
                .orderByDesc(TmsCustomerOrderEntity::getCreateTime);

        // 根据状态进行查询
        if (deliveryOrderDTO.getStatus() != null) {
            switch (deliveryOrderDTO.getStatus()) {
                case 12: // 配送中 - 状态码为4,5,11
                    wrapper.in(TmsCustomerOrderEntity::getOrderStatus, Arrays.asList(4, 5, 11));
                    break;
                case 6: // 已完成 - 状态码为6
                    wrapper.eq(TmsCustomerOrderEntity::getOrderStatus, NewOrderStatus.COMPLETED.getCode());
                    break;
                case 10: // 配送失败 - 状态码为10 这里可能会有子单配送失败的可能，所以配送失败的情况单独进行查询
                    // 先查询该司机的主单号
                    List<TmsCustomerOrderEntity> mainOrders = customerOrderMapper.selectList(
                            new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                                    .eq(TmsCustomerOrderEntity::getDeliveryDriverId, deliveryOrderDTO.getDriverId())
                                    .eq(TmsCustomerOrderEntity::getSubFlag, Boolean.FALSE)
                                    .eq(TmsCustomerOrderEntity::getIsDelete, 0)
                                    .select(TmsCustomerOrderEntity::getCustomerOrderNumber)
                    );
                    
                    if (CollUtil.isEmpty(mainOrders)) {
                        return new Page<>(page.getCurrent(), page.getSize(), 0);
                    }
                    
                    List<String> mainOrderNumbers = mainOrders.stream()
                            .map(TmsCustomerOrderEntity::getCustomerOrderNumber)
                            .collect(Collectors.toList());
                    
                    // 查询这些主单下的配送失败的子单
                    MPJLambdaWrapper<TmsCustomerOrderEntity> failedWrapper = new MPJLambdaWrapper<>();
                    failedWrapper.selectAll(TmsCustomerOrderEntity.class)
                            .eq(TmsCustomerOrderEntity::getOrderStatus, NewOrderStatus.FAILED_DELIVERY.getCode())
                            .eq(TmsCustomerOrderEntity::getSubFlag, Boolean.TRUE)
                            .in(TmsCustomerOrderEntity::getCustomerOrderNumber, mainOrderNumbers)
                            .orderByDesc(TmsCustomerOrderEntity::getCreateTime);
                    
                    return customerOrderMapper.selectJoinPage(page, TmsCustomerOrderEntity.class, failedWrapper);
                default:
                    // 如果没有指定状态，查询所有状态
                    break;
            }
        }

        return customerOrderMapper.selectJoinPage(page, TmsCustomerOrderEntity.class, wrapper);
    }

}
package com.jygjexp.jynx.tms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.security.util.SecurityUtils;
import com.jygjexp.jynx.tms.abo.TmsCustomerReservationPickUpAbo;
import com.jygjexp.jynx.tms.dto.*;
import com.jygjexp.jynx.tms.entity.TmsCustomerEntity;
import com.jygjexp.jynx.tms.entity.TmsCustomerReservationPickUpEntity;
import com.jygjexp.jynx.tms.entity.TmsOverAreaEntity;
import com.jygjexp.jynx.tms.enums.CustomerPickUpOrderStatusEnum;
import com.jygjexp.jynx.tms.exception.CustomBusinessException;
import com.jygjexp.jynx.tms.mapper.TmsCustomerReservationPickUpMapper;
import com.jygjexp.jynx.tms.model.bo.PageBo;
import com.jygjexp.jynx.tms.model.bo.QueryCondition;
import com.jygjexp.jynx.tms.qbo.TmsCustomerReservationPickUpQbo;
import com.jygjexp.jynx.tms.response.LocalizedR;
import com.jygjexp.jynx.tms.service.EmailSendAsyncService;
import com.jygjexp.jynx.tms.service.TmsCustomerReservationPickUpService;
import com.jygjexp.jynx.tms.service.TmsOrderScanRecordService;
import com.jygjexp.jynx.tms.utils.PickUpImportValidator;
import com.jygjexp.jynx.tms.utils.SelectUtil;
import com.jygjexp.jynx.tms.utils.ServiceUtil;
import com.jygjexp.jynx.tms.vo.GeocodeResult;
import com.jygjexp.jynx.tms.vo.TmsCustomerReservationPickUpVo;
import com.jygjexp.jynx.tms.vo.TmsPathPlanVo;
import lombok.RequiredArgsConstructor;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 客户预约取件
 *
 * <AUTHOR>
 * @date 2025-07-16 10:47:32
 */
@Service
@RequiredArgsConstructor
public class TmsCustomerReservationPickUpServiceImpl extends MPJBaseServiceImpl<TmsCustomerReservationPickUpMapper, TmsCustomerReservationPickUpEntity> implements TmsCustomerReservationPickUpService {

    private final EmailSendAsyncService emailSendAsyncService;

    private final TmsOperateLogServiceImpl tmsOperateLogService;

    private final TmsOrderScanRecordService orderScanRecordService;

    @Override
    public IPage<TmsCustomerReservationPickUpVo> pageData(QueryCondition<TmsCustomerReservationPickUpQbo> qbo) {
        PageBo pageBo = new PageBo(qbo.getSize(), qbo.getCurrent());
        IPage<TmsCustomerReservationPickUpVo> joinPage = ServiceUtil.findJoinPage(pageBo, this, this.getQueryWrapper(qbo), TmsCustomerReservationPickUpVo.class);
        buildVO(joinPage.getRecords());
        return joinPage;
    }

    @Override
    public R saveData(TmsCustomerReservationPickUpAbo abo) {
        // 判断所填的邮政编码 是否 是存在于配置的大区内
        List<String> allFirstCode = SelectUtil.getAllFirstCode();
        boolean isCode = allFirstCode.contains(abo.getPostalCode().substring(0, 3).toUpperCase());
        if (!isCode) {
            return LocalizedR.failed("tms.customer.reservation.pickup.address.errors", "");
        }

        // 地址 + 邮编 转经纬度
        GeocodeResult geocodeResult = SelectUtil.getLatLngByAddress(abo.getAddressLine(), abo.getPostalCode());
        if (StrUtil.isBlank(geocodeResult.getLocation())) {
            return LocalizedR.failed("tms.customer.reservation.pickup.address.errors", "");
        }

        // 生成委托单号
        TmsCustomerEntity customer = SelectUtil.getCustomerByUserId(abo.getCustomerId());
        if (customer == null) {
            return LocalizedR.failed("tms.customer.reservation.pickup.account.errors", "");
        }
        String orderNo = SelectUtil.getOrderNo(customer.getId(), 1);
        if (orderNo != null && !orderNo.isEmpty()) {
            orderNo = "P" + orderNo.substring(1);
        }
        TmsCustomerReservationPickUpEntity result = BeanUtil.copyProperties(abo, TmsCustomerReservationPickUpEntity.class);
        result.setReservationNumber(orderNo);
        result.setLocation(geocodeResult.getLocation());
        result.setCustomerId(customer.getId());
        save(result);

        if (StrUtil.isBlank(customer.getEmail())) {
            return R.ok();
        }
        // 发送邮件
        emailSendAsyncService.sendPickupEmail(result, Collections.singletonList(customer.getEmail()));
        return R.ok();
    }


    @Override
    public TmsCustomerReservationPickUpVo getDataById(Long id) {
        TmsCustomerReservationPickUpEntity one = getById(id);
        TmsCustomerReservationPickUpVo vo = BeanUtil.copyProperties(one, TmsCustomerReservationPickUpVo.class);
        buildVO(Collections.singletonList(vo));
        return vo;
    }

    @Override
    public R updateDataById(TmsCustomerReservationPickUpEntity abo) {
        if(StrUtil.isNotBlank(abo.getAddressLine())){
            // 地址 + 邮编 转经纬度
            GeocodeResult geocodeResult = SelectUtil.getLatLngByAddress(abo.getAddressLine(), abo.getPostalCode());
            if(StrUtil.isBlank(geocodeResult.getLocation()) || StrUtil.isBlank(geocodeResult.getCountry()) || !ObjUtil.equal(geocodeResult.getCountry(),"Canada")){
                return LocalizedR.failed("tms.customer.reservation.pickup.address.errors","");
            }
            abo.setLocation(geocodeResult.getLocation());
        }
        updateById(abo);
        return R.ok();
    }

    @Override
    public TmsPathPlanVo pathPlanList(QueryCondition<TmsCustomerReservationPickUpQbo> qbo) {
        qbo.getQuery().setIsAssignDriver(true);
        List<TmsCustomerReservationPickUpVo> joinList = ServiceUtil.findJoinList(this, this.getQueryWrapper(qbo), TmsCustomerReservationPickUpVo.class);
        buildVO(joinList);

        List<TmsOverAreaEntity> overAreaList = SelectUtil.getOverAreaList(qbo.getQuery().getPortCode());

        Map<String, String> prefixToNameMap = new HashMap<>();
        for (TmsOverAreaEntity overArea : overAreaList) {
            if (StrUtil.isBlank(overArea.getZip()) || StrUtil.isBlank(overArea.getName())) {
                continue;
            }
            String[] prefixes = overArea.getZip().split(",");
            for (String prefix : prefixes) {
                prefixToNameMap.put(prefix.trim(), overArea.getName());
            }
        }
        // 分组
        Map<String, List<TmsCustomerReservationPickUpVo>> resultMap = new HashMap<>();
        for (TmsCustomerReservationPickUpVo vo : joinList) {
            if (StrUtil.isBlank(vo.getPostalCode()) || vo.getPostalCode().length() < 3) {
                continue;
            }
            String prefix = vo.getPostalCode().substring(0, 3).toUpperCase();
            String name = prefixToNameMap.get(prefix);
            if (name != null) {
                resultMap.computeIfAbsent(name, k -> new ArrayList<>()).add(vo);
            }
        }
        TmsPathPlanVo result = new TmsPathPlanVo();
        result.setPickUpPlanMap(resultMap);
        result.setTotalOrders(
                CollUtil.isNotEmpty(resultMap) ?
                count(Wrappers.<TmsCustomerReservationPickUpEntity>lambdaQuery()
                        .isNull(TmsCustomerReservationPickUpEntity::getDriverId)
                        .eq(TmsCustomerReservationPickUpEntity::getStatus, CustomerPickUpOrderStatusEnum.TBPU.getCode()))
                : 0);
        return result;
    }

    @Override
    @Transactional
    public void pathPlanDriver(PickUpPathPlanDriverDto dto) {
        List<TmsCustomerReservationPickUpEntity> list = list(Wrappers.<TmsCustomerReservationPickUpEntity>lambdaQuery()
                .in(TmsCustomerReservationPickUpEntity::getReservationNumber, dto.getReservationNumberList()));
        list.forEach(entity -> entity.setDriverId(dto.getDriverId()));
        updateBatchById(list);
    }

    @Override
    public Boolean appScanning(AppScanningDTO dto) {
        Boolean orderNoExist = SelectUtil.isOrderNoExist(dto.getOrderNo());
        if(orderNoExist){
            orderScanRecordService.saveRecord(dto);
        }
        return orderNoExist;
    }

    @Override
    public TmsCustomerReservationPickUpVo getDataByReservationNumber(String reservationNumber) {
        TmsCustomerReservationPickUpEntity one = getOne(Wrappers.<TmsCustomerReservationPickUpEntity>lambdaQuery()
                .eq(TmsCustomerReservationPickUpEntity::getReservationNumber, reservationNumber));
        TmsCustomerReservationPickUpVo vo = BeanUtil.copyProperties(one, TmsCustomerReservationPickUpVo.class);
        buildVO(Collections.singletonList(vo));
        return vo;
    }

    @Override
    public List<TmsCustomerReservationPickUpVo> mapLabel(QueryCondition<TmsCustomerReservationPickUpQbo> qbo) {
        List<TmsCustomerReservationPickUpVo> joinList = ServiceUtil.findJoinList(this, this.getQueryWrapper(qbo), TmsCustomerReservationPickUpVo.class);
        buildVO(joinList);
        return joinList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R importData(MultipartFile file) {
        List<String> errorMessages = new ArrayList<>();
        List<TmsCustomerReservationPickUpImportDTO> dataList = new ArrayList<>();
        AtomicInteger totalRowIndex = new AtomicInteger(2);

        // 预加载校验数据
        Map<Long, String> customerNameMap = SelectUtil.getCustomerNameMap();
        List<String> allFirstCode = SelectUtil.getAllFirstCode();
        try {
            EasyExcel.read(file.getInputStream(), TmsCustomerReservationPickUpImportDTO.class,
                    new PageReadListener<TmsCustomerReservationPickUpImportDTO>(dataBatch -> {
                        // 调用校验方法
                        validateImportData(dataBatch, totalRowIndex, errorMessages, customerNameMap, allFirstCode);
                        dataList.addAll(dataBatch);
                    })).sheet().headRowNumber(2).doRead();
        } catch (IOException e) {
            return R.failed("文件读取失败：" + e.getMessage());
        }
        // 统一返回错误信息
        if (!errorMessages.isEmpty()) {
            return R.failed("导入出现错误：" + String.join("; ", errorMessages));
        }
        // 保存数据
        return savePickUpData(dataList) ? R.ok("成功导入 " + dataList.size() + " 条数据") : R.failed("数据保存失败");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R helpPlaceOrder(TmsCustomerReservationPickUpAbo abo) {
        // 判断所填的邮政编码 是否 是存在于配置的大区内
        List<String> allFirstCode = SelectUtil.getAllFirstCode();
        boolean isCode = allFirstCode.contains(abo.getPostalCode().substring(0, 3).toUpperCase());
        if (!isCode) {
            return LocalizedR.failed("tms.customer.reservation.pickup.address.errors", "");
        }

        // 地址 + 邮编 转经纬度
        GeocodeResult geocodeResult = SelectUtil.getLatLngByAddress(abo.getAddressLine(), abo.getPostalCode());
        if (StrUtil.isBlank(geocodeResult.getLocation())) {
            return LocalizedR.failed("tms.customer.reservation.pickup.address.errors", "");
        }

        // 生成委托单号
        TmsCustomerEntity customer = SelectUtil.getCustomerByUserId(abo.getCustomerId());
        if (customer == null) {
            return LocalizedR.failed("tms.customer.reservation.pickup.account.errors", "");
        }
        String orderNo = SelectUtil.getOrderNo(customer.getId(), 1);
        if (orderNo != null && !orderNo.isEmpty()) {
            orderNo = "P" + orderNo.substring(1);
        }
        //如果总重量小于32kg，那么一定没有超过32kg重量的物品
        if (abo.getWeight().compareTo(new BigDecimal("32")) <= 0) {
            abo.setWeightStatus(false);
        }
        TmsCustomerReservationPickUpEntity result = BeanUtil.copyProperties(abo, TmsCustomerReservationPickUpEntity.class);
        result.setReservationNumber(orderNo);
        result.setLocation(geocodeResult.getLocation());
        result.setCustomerId(customer.getId());
        save(result);

        if (StrUtil.isBlank(customer.getEmail())) {
            return R.ok();
        }
        emailSendAsyncService.sendHelpPickup(result, Collections.singletonList(customer.getEmail()));
        //发送短信通知
//        sendReservationPickUpSms(result);
        return R.ok();
    }

//    @Async
//    public void sendReservationPickUpSms(TmsCustomerReservationPickUpEntity result) {
//        SysSmsSendDto sysSmsSendDto = new SysSmsSendDto();
//        sysSmsSendDto.setType("");
//        sysSmsSendDto.setProjectName("jynx-tms-biz");
//        sysSmsSendDto.setMobile(result.getTelephone());
//        StringBuilder content = new StringBuilder();
//        content.append("Hi %s\n");
//        content.append("This e-mail confirms that your pickup reguest\n");
//        content.append("Has peen scheduled.Your pickupconfirmation number is %s\n");
//        content.append("Pickup Details\n");
//        content.append("Date:%s\n");
//        content.append("Pickup Windows:Between %s and %s\n");
//        content.append("Address: %s\n");
//        content.append("Shipment Details\n");
//        content.append("Total Weight:%s Kg.Number of Pieces:%s\n");
//        content.append("NB Packaging Guide\n");
//        content.append("Follow these guidelines to prepare your shipment for pickup.\n");
//        content.append("Download now\n");
//        content.append("Need to make a change to your pickuprequest?\n");
//        content.append("To make a change,including,pickup timeocation for pickup,shipment weight\n");
//        content.append("andnumber of pieces,visit Purolator's virtuaassistant.\n");
//        content.append("Modify pickup");
//        String realContent = String.format(content.toString(),
//                result.getCustomerName(), result.getReservationNumber(), result.getPickupDate(), result.getStartPickupTime(), result.getEndPickupTime(), result.getAddressLine(), result.getWeight(), result.getPackageNum());
//        sysSmsSendDto.setMessage(realContent);
////        remoteTmsSendSmsService.sendSms(sysSmsSendDto);
//    }

    private Boolean savePickUpData(List<TmsCustomerReservationPickUpImportDTO> dataList) {
        try {
            List<TmsCustomerReservationPickUpEntity> entityList = new ArrayList<>();

            // 获取客户映射 (客户名称 -> 客户对象)
            Map<String, TmsCustomerEntity> customerNameMap = SelectUtil.getCustomerByNameMap();

            for (TmsCustomerReservationPickUpImportDTO dto : dataList) {
                // 获取客户信息
                TmsCustomerEntity customer = customerNameMap.get(dto.getCustomerName());

                // 地址转经纬度
                GeocodeResult geocodeResult = SelectUtil.getLatLngByAddress(dto.getAddressLine(), dto.getPostalCode());
                if (StrUtil.isBlank(geocodeResult.getLocation())) {
                    // 地址解析失败时的默认处理
                    throw new CustomBusinessException(LocalizedR.getMessage("tms.customer.reservation.pickup.address.errors", null));
                }

                // 生成委托单号
                String orderNo = SelectUtil.getOrderNo(customer.getId(), 1);
                if (orderNo != null && !orderNo.isEmpty()) {
                    orderNo = "P" + orderNo.substring(1);
                }

                // 构建实体对象
                TmsCustomerReservationPickUpEntity entity = new TmsCustomerReservationPickUpEntity();
                entity.setCustomerId(customer.getId());
                entity.setCustomerName(dto.getCustomerName());
                entity.setReservationNumber(orderNo);
                entity.setContactName(dto.getContactName());
                entity.setTelephone(dto.getTelephone());
                entity.setCity(dto.getCity());
                entity.setPostalCode(dto.getPostalCode());
                entity.setAddressLine(dto.getAddressLine());
                entity.setLocation(geocodeResult.getLocation());
                entity.setPackageNum(PickUpImportValidator.convertToInteger(dto.getPackageNum()));
                entity.setWeight(PickUpImportValidator.convertToBigDecimal(dto.getWeight()));
                entity.setWeightStatus("yes".equalsIgnoreCase(dto.getWeightStatus()) ? Boolean.TRUE : Boolean.FALSE);
                entity.setPickupDate(PickUpImportValidator.convertToLocalDate(dto.getPickupDate()));
                entity.setStartPickupTime(PickUpImportValidator.convertToLocalDateTime(dto.getPickupDate(), dto.getStartPickupTime()));
                entity.setEndPickupTime(PickUpImportValidator.convertToLocalDateTime(dto.getPickupDate(), dto.getEndPickupTime()));
                // 默认待分配状态
                entity.setStatus(CustomerPickUpOrderStatusEnum.TBPU.getCode());
                entity.setPickupReference(dto.getPickupReference());
                entity.setSpecialInstructions(dto.getSpecialInstructions());

                entityList.add(entity);
            }

            // 批量保存
            return saveBatch(entityList);
        } catch (Exception e) {
            throw new RuntimeException("数据保存失败：" + e.getMessage());
        }
    }

    private void buildVO(List<TmsCustomerReservationPickUpVo> records) {
        Map<Long, TmsCustomerEntity> customerMap = SelectUtil.getCustomerMap();
        records.forEach(vo -> {
            vo.setStatusStr(CustomerPickUpOrderStatusEnum.getInstance(vo.getStatus()).getInfo());
            vo.setCustomer(customerMap.get(vo.getCustomerId()));
        });
    }

    private MPJLambdaWrapper<TmsCustomerReservationPickUpEntity> getQueryWrapper(QueryCondition<TmsCustomerReservationPickUpQbo> qbo) {
        MPJLambdaWrapper<TmsCustomerReservationPickUpEntity> wrapper = new MPJLambdaWrapper<>();

        TmsCustomerReservationPickUpQbo query = qbo.getQuery();

        boolean date = ObjUtil.isNotEmpty(query.getStartPickupDate()) && ObjUtil.isNotEmpty(query.getEndPickupDate());
        boolean time = ObjUtil.isNotEmpty(query.getStartTime()) && ObjUtil.isNotEmpty(query.getEndTime());

        wrapper
                .eq(ObjUtil.isNotEmpty(query.getDriverId()), TmsCustomerReservationPickUpEntity::getDriverId, query.getDriverId())
                .in(CollUtil.isNotEmpty(query.getReservationNumbers()), TmsCustomerReservationPickUpEntity::getReservationNumber, query.getReservationNumbers())
                .in(CollUtil.isNotEmpty(query.getStatusList()), TmsCustomerReservationPickUpEntity::getStatus, query.getStatusList())
                .eq(ObjUtil.isNotEmpty(query.getStatus()), TmsCustomerReservationPickUpEntity::getStatus, query.getStatus())
                .between(date, TmsCustomerReservationPickUpEntity::getPickupDate, query.getStartPickupDate(), query.getEndPickupDate())
                .between(time, TmsCustomerReservationPickUpEntity::getCreateTime, query.getStartTime(), query.getEndTime())
                .like(StrUtil.isNotBlank(query.getReservationNumber()), TmsCustomerReservationPickUpEntity::getReservationNumber, query.getReservationNumber())
                .like(ObjUtil.isNotEmpty(query.getContactName()), TmsCustomerReservationPickUpEntity::getContactName, query.getContactName())
                .like(StrUtil.isNotBlank(query.getTelephone()), TmsCustomerReservationPickUpEntity::getTelephone, query.getTelephone())
                .like(StrUtil.isNotBlank(query.getCity()), TmsCustomerReservationPickUpEntity::getCity, query.getCity())
                .orderByDesc(TmsCustomerReservationPickUpEntity::getCreateTime);

        // 揽收规划查询
        wrapper
                .isNull(query.getIsAssignDriver(), TmsCustomerReservationPickUpEntity::getDriverId)
                .eq(query.getIsAssignDriver(), TmsCustomerReservationPickUpEntity::getStatus, CustomerPickUpOrderStatusEnum.TBPU.getCode());

        // 客户端/管理端
        if (query.getClientType() != null && query.getClientType() == 2) {
            // 客户端
            TmsCustomerEntity customer = SelectUtil.getCustomerByUserId(SecurityUtils.getUser().getId());
            wrapper.eq(ObjUtil.isNotEmpty(customer), TmsCustomerReservationPickUpEntity::getCustomerId, customer.getId());
        }

        return wrapper;
    }

    /**
     * 提取的校验方法
     */
    private void validateImportData(List<TmsCustomerReservationPickUpImportDTO> dataBatch,
                                    AtomicInteger totalRowIndex,
                                    List<String> errorMessages,
                                    Map<Long, String> customerNameMap,
                                    List<String> allFirstCode) {

        for (TmsCustomerReservationPickUpImportDTO data : dataBatch) {
            int currentRow = totalRowIndex.incrementAndGet();

            // 基础字段校验
            PickUpImportValidator.validateField(data.getCustomerName(), "客户名称", currentRow, errorMessages);
            PickUpImportValidator.validateField(data.getName(), "联系人", currentRow, errorMessages);
            PickUpImportValidator.validateField(data.getCity(), "国家/城市", currentRow, errorMessages);
            PickUpImportValidator.validateField(data.getPostalCode(), "邮编", currentRow, errorMessages);
            PickUpImportValidator.validateField(data.getAddressLine(), "详细地址", currentRow, errorMessages);
            PickUpImportValidator.validateField(data.getWeightStatus(), "是否超过32KG", currentRow, errorMessages);
            // 手机号格式校验
            PickUpImportValidator.validatePhoneNumber(data.getTelephone(), currentRow, errorMessages);

            // 数值字段格式校验
            PickUpImportValidator.validatePackageNumFormat(data.getPackageNum(), currentRow, errorMessages);
            PickUpImportValidator.validateWeightFormat(data.getWeight(), currentRow, errorMessages);

            // 时间字段格式校验
            PickUpImportValidator.validateDateFormat(data.getPickupDate(), "取件日期", currentRow, errorMessages);
            PickUpImportValidator.validateTimeFormat(data.getStartPickupTime(), "最早取件时间", currentRow, errorMessages);
            PickUpImportValidator.validateTimeFormat(data.getEndPickupTime(), "最晚取件时间", currentRow, errorMessages);

            // 时间逻辑校验
            PickUpImportValidator.validateTimeLogic(data.getStartPickupTime(), data.getEndPickupTime(), currentRow, errorMessages);
            // 业务规则校验
            PickUpImportValidator.validateWeightStatus(data.getWeightStatus(), currentRow, errorMessages);
            PickUpImportValidator.validateCustomerExists(data.getCustomerName(), customerNameMap, currentRow, errorMessages);
            PickUpImportValidator.validatePostalCode(data.getPostalCode(), allFirstCode, currentRow, errorMessages);
        }
    }
}
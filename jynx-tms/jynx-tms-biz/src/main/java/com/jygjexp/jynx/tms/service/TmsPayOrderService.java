package com.jygjexp.jynx.tms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.tms.entity.TmsPayOrderEntity;

import java.util.List;
import java.util.Map;

public interface TmsPayOrderService extends IService<TmsPayOrderEntity> {

    //创建支付订单
     boolean createPayOrder(TmsPayOrderEntity payOrder);

     // 根据流水单号查询支付订单
     Map<String,TmsPayOrderEntity> getMapByPayOrderIds(List<String> payOrderIds);

}

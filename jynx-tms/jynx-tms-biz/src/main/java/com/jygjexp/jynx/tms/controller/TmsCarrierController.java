package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.admin.api.dto.UserDTO;
import com.jygjexp.jynx.admin.api.dto.UserInfo;
import com.jygjexp.jynx.admin.api.feign.RemoteUserService;
import com.jygjexp.jynx.common.core.constant.SecurityConstants;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.common.security.annotation.Inner;
import com.jygjexp.jynx.tms.entity.TmsCarrierEntity;
import com.jygjexp.jynx.tms.feign.RemoteTmsUpmsService;
import com.jygjexp.jynx.tms.response.LocalizedR;
import com.jygjexp.jynx.tms.service.TmsCarrierService;
import com.jygjexp.jynx.tms.vo.TmsCarrierPageVo;
import com.jygjexp.jynx.tms.vo.excel.TmsCarrierExportVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 承运商信息记录
 *
 * <AUTHOR>
 * @date 2025-02-21 16:13:23
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsCarrier" )
@Tag(description = "tmsCarrier" , name = "承运商信息记录管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsCarrierController {

    private final  TmsCarrierService tmsCarrierService;
    private final RemoteTmsUpmsService remoteTmsUpmsService;


    /**
     * 分页查询
     * @param page 分页对象
     * @param vo 承运商信息记录
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    //@SentinelResource(value = "createOrderResource", blockHandler = "handleBlock")
    @PreAuthorize("@pms.hasPermission('tms_tmsCarrier_view')" )
    public R getTmsCarrierPage(@ParameterObject Page page, @ParameterObject TmsCarrierPageVo vo) {
        return R.ok(tmsCarrierService.search(page, vo));
    }


/*    public R handleBlock(BlockException ex) {
        return R.failed("请求过于频繁，请稍后再试");
    }*/


    /**
     * 通过id查询承运商信息记录
     * @param carrierId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{carrierId}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsCarrier_view')" )
    public R getById(@PathVariable("carrierId" ) Long carrierId) {
        return R.ok(tmsCarrierService.getById(carrierId));
    }

    /**
     * 新增承运商信息记录
     * @param tmsCarrier 承运商信息记录
     * @return R
     */
    @Operation(summary = "新增承运商信息记录" , description = "新增承运商信息记录" )
    @SysLog("新增承运商信息记录" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsCarrier_add')" )
    public R save(@RequestBody TmsCarrierEntity tmsCarrier) {
        return tmsCarrierService.saveCarrier(tmsCarrier);
    }

    /**
     * 修改承运商信息记录
     * @param tmsCarrier 承运商信息记录
     * @return R
     */
    @Operation(summary = "修改承运商信息记录" , description = "修改承运商信息记录" )
    @SysLog("修改承运商信息记录" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsCarrier_edit')" )
    public R updateById(@RequestBody TmsCarrierEntity tmsCarrier) {
        return tmsCarrierService.updateCarrier(tmsCarrier);
    }

    /**
     * 通过id删除承运商信息记录
     * @param ids cirId列表
     * @return R
     */
    @Operation(summary = "通过id删除承运商信息记录" , description = "通过id删除承运商信息记录" )
    @SysLog("通过id删除承运商信息记录" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsCarrier_del')" )
    public R removeById(@RequestBody Long[] ids) {
        // 根据ids批量查询承运商信息
        List<TmsCarrierEntity> carrierList = tmsCarrierService.listByIds(CollUtil.toList(ids));
        for (TmsCarrierEntity carrier : carrierList) {
            // 根据承运商名称查询系统后台用户账号
            R<UserInfo> info = remoteTmsUpmsService.info(carrier.getCarrierName());
            if (info.getCode() == 0) {
                remoteTmsUpmsService.userDel(new Long[]{info.getData().getSysUser().getUserId()});
            }
        }
        return R.ok(tmsCarrierService.removeBatchByIds(CollUtil.toList(ids)));
    }


    // 查询全部承运商
    @Operation(summary = "查询全部承运商" , description = "查询全部承运商" )
    @GetMapping("/listCarrier" )
//    @PreAuthorize("@pms.hasPermission('tms_tmsCarrier_view')" )
    public R listCarrier() {
        LambdaQueryWrapper<TmsCarrierEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(TmsCarrierEntity::getCarrierId,TmsCarrierEntity::getCarrierName, TmsCarrierEntity::getCarrierCode
                ,TmsCarrierEntity::getCarrierType,TmsCarrierEntity::getIsValid) ;// 指定查询字段
        List<TmsCarrierEntity> result = tmsCarrierService.list(wrapper);
        return R.ok(result);
    }


    // 查询承运商是否存在
    @Operation(summary = "查询承运商是否存在" , description = "查询承运商是否存在" )
    @GetMapping("/getCarrierName" )
    public R getCarrierName(String carrierName) {TmsCarrierEntity carrierNameEntity = tmsCarrierService.getOne(new LambdaQueryWrapper<TmsCarrierEntity>()
                .eq(TmsCarrierEntity::getCarrierName,carrierName), false);
        if (null == carrierNameEntity){
            return R.ok();
        }
        return R.ok(carrierNameEntity);
    }

    /**
     * 启用客户
     *
     * @param id id
     * @return R
     */
    @Operation(summary = "启用客户", description = "启用客户")
    @GetMapping("/enableById/{id}")
    public R enableById(@PathVariable("id") Long id) {
        return R.ok(tmsCarrierService.enableById(id));
    }

    /**
     * 停用客户
     *
     * @param id id
     * @return R
     */
    @Operation(summary = "停用客户", description = "停用客户")
    @GetMapping("/disableById/{id}")
    public R disableById(@PathVariable("id") Long id) {
        return R.ok(tmsCarrierService.disableById(id));
    }


    /**
     * 导出excel 表格
     * @param tmsCarrier 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsCarrier_export')" )
    public List<TmsCarrierExportVo> export(TmsCarrierPageVo tmsCarrier, Long[] ids) {
        return tmsCarrierService.getExcel(tmsCarrier,ids);
    }



    @Operation(summary = "按客户/委托订单查询承运商列表", description = "按客户/委托订单查询承运商列表")
    @PostMapping("/listCarriersByOrder")
    public R<List<TmsCarrierEntity>> listCarriersByOrder(
            @RequestParam(required = false) String customerOrderNumber, // 客户单号
            @RequestParam(required = false) String entrustedOrderNumber // 委托单号
    ) {
        return R.ok(tmsCarrierService.listCarriersByOrder(customerOrderNumber, entrustedOrderNumber));
    }

}
package com.jygjexp.jynx.tms.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.entity.TmsAgentEntity;
import com.jygjexp.jynx.tms.vo.TmsAgentPageVo;
import com.jygjexp.jynx.tms.vo.TmsAgentUpdateVo;


public interface TmsAgentService extends IService<TmsAgentEntity> {

    /**
     * 分页
     *
     * @param vo
     * @return
     */
    Page<TmsAgentEntity> search(TmsAgentPageVo vo);

    /**
     * 创建代理商
     *
     * @param entity
     * @return
     */
    R<Boolean> create(TmsAgentEntity entity);

    /**
     * 编辑
     *
     * @param entity
     * @return
     */
    Object updated(TmsAgentUpdateVo entity);

    /**
     * 启用
     *
     * @param id
     * @return
     */
    R<Boolean> enable(Long id);

    /**
     * 禁用
     * @param id
     * @return
     */
    R<Boolean> disable(Long id);
}
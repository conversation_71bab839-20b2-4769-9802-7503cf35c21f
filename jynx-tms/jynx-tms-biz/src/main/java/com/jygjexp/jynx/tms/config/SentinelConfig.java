package com.jygjexp.jynx.tms.config;

import com.alibaba.csp.sentinel.slots.block.RuleConstant;
import com.alibaba.csp.sentinel.slots.block.flow.FlowRule;
import com.alibaba.csp.sentinel.slots.block.flow.FlowRuleManager;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

/*   暂时注释别删除
@Component
public class SentinelConfig {

    */
/*
    *           createOrderResource:资源名   blockHandler：限流熔断后的补偿接口
        @SentinelResource(value = "createOrderResource", blockHandler = "handleBlock")
    * *//*


    @PostConstruct
    public void initFlowRules() {
        List<FlowRule> rules = new ArrayList<>();

        // 接口1
        FlowRule rule1 = new FlowRule();
        rule1.setResource("createOrderResource");
        rule1.setGrade(RuleConstant.FLOW_GRADE_QPS);
        rule1.setCount(1);
        rules.add(rule1);

*/
/*        // 接口2
        FlowRule rule2 = new FlowRule();
        rule2.setResource("getOrderResource");
        rule2.setGrade(RuleConstant.FLOW_GRADE_QPS);
        rule2.setCount(10);
        rules.add(rule2);

        // 接口3
        FlowRule rule3 = new FlowRule();
        rule3.setResource("updateOrderResource");
        rule3.setGrade(RuleConstant.FLOW_GRADE_QPS);
        rule3.setCount(3);
        rules.add(rule3);*//*


        // 加载规则
        FlowRuleManager.loadRules(rules);
    }
}*/

package com.jygjexp.jynx.tms.model.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 支付接口参数业务类
 */
@Data
public class PayParaVo {

    @Schema(description="金额,单位必须为分，正整数，不能出现小数")
    private BigDecimal amount;

    @Schema(description="支付类型WX_NATIVE、ALIPAY_QR")
    private String payType;

    @Schema(description="用户ID")
    private Long userId;

    @Schema(description="订单流水号，退款时用")
    private String payOrderId;

    @Schema(description="成功页面")
    private String successPage;

    @Schema(description="取消页面")
    private String cancelPage;
}

package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.dto.app.ListDeliveryOrderDTO;
import com.jygjexp.jynx.tms.entity.TmsTransportTaskOrderEntity;
import com.jygjexp.jynx.tms.service.TmsTransportTaskOrderService;
import com.jygjexp.jynx.tms.vo.TmsTransportTaskOrderPageVo;
import com.jygjexp.jynx.tms.vo.excel.TmsTransportDeliveryTaskExcelVo;
import com.jygjexp.jynx.tms.vo.excel.TmsTransportTaskExcelVo;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 运输任务单
 *
 * <AUTHOR>
 * @date 2025-04-07 15:30:22
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsTransportTaskOrder" )
@Tag(description = "tmsTransportTaskOrder" , name = "运输任务单管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsTransportTaskOrderController {

    private final  TmsTransportTaskOrderService tmsTransportTaskOrderService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param vo 运输任务单
     * @return
     */
    @Operation(summary = "分页查询运输任务单" , description = "分页查询运输任务单" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsTransportTaskOrder_view')" )
    public R getTmsTransportTaskOrderPage(@ParameterObject Page page, @ParameterObject TmsTransportTaskOrderPageVo vo) {
        return R.ok(tmsTransportTaskOrderService.search(page, vo));
    }


    /**
     * 通过id查询运输任务单
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsTransportTaskOrder_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return tmsTransportTaskOrderService.selectById(id);
    }

    /**
     * 修改运输任务单
     * @param tmsTransportTaskOrder 运输任务单
     * @return R
     */
    @Operation(summary = "修改运输任务单" , description = "修改运输任务单" )
    @SysLog("修改运输任务单" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsTransportTaskOrder_edit')" )
    public R updateById(@RequestBody TmsTransportTaskOrderEntity tmsTransportTaskOrder) {
        return R.ok(tmsTransportTaskOrderService.updateById(tmsTransportTaskOrder));
    }

    /**
     * 通过id删除运输任务单
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除运输任务单" , description = "通过id删除运输任务单" )
    @SysLog("通过id删除运输任务单" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsTransportTaskOrder_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsTransportTaskOrderService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 通过订单id查询订单详情
     * @param id id
     * @return R
     */
    @Operation(summary = "通过订单id查询订单详情" , description = "通过订单id查询订单详情" )
    @GetMapping("orderList/{id}/{isTask}" )
    public R getByOrderList(@PathVariable("id" ) Long id, @PathVariable("isTask" ) Integer isTask) {
        return tmsTransportTaskOrderService.selectByOrderId(id,isTask);
    }

    /**
     * 根据派送任务批次号查询该任务中的所有订单
     */
    @Operation(summary = "根据派送任务批次号查询该任务中的所有订单" , description = "根据派送任务批次号查询该任务中的所有订单" )
    @GetMapping("/getDriverDeliveryOrderByTaskOrderNo" )
    public R getDriverDeliveryOrderByTaskOrderNo(@RequestParam(value = "taskOrderNo",required = true) String  taskOrderNo) {
        return tmsTransportTaskOrderService.getDriverDeliveryOrderByTaskOrderNo(taskOrderNo);
    }

    @Operation(summary = "手机app根据分页和订单状态查询所有订单" , description = "手机app根据分页和订单状态查询所有订单" )
    @GetMapping("/listDriverDeliveryOrder" )
    public R listDriverDeliveryOrder(@ParameterObject Page page, @ParameterObject ListDeliveryOrderDTO deliveryOrderDTO){
        return R.ok(tmsTransportTaskOrderService.listDriverDeliveryOrder(page,deliveryOrderDTO));
    }


    /**
     * 包裹查询
     */
    @Operation(summary = "包裹查询" , description = "包裹查询" )
    @GetMapping("/packageQuery" )
    public R packageQuery(String orderNo) {
        return tmsTransportTaskOrderService.packageQuery(orderNo);
    }

    /**
     * 导出揽收任务excel 表格
     * @param tmsTransportTaskOrder 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsTransportTaskOrder_export')" )
    public List<TmsTransportTaskExcelVo> export(TmsTransportTaskOrderPageVo tmsTransportTaskOrder, Long[] ids) {
        return tmsTransportTaskOrderService.getTransportTaskExcel(tmsTransportTaskOrder,ids);
    }

    /**
     * 导出派送任务excel 表格
     * @param tmsTransportTaskOrder 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/delivery/export")
    @PreAuthorize("@pms.hasPermission('tms_transportTaskOrder_delivery')" )
    public List<TmsTransportDeliveryTaskExcelVo> deliveryExport(TmsTransportTaskOrderPageVo tmsTransportTaskOrder, Long[] ids) {
        return tmsTransportTaskOrderService.getDeliveryExport(tmsTransportTaskOrder,ids);
    }
}
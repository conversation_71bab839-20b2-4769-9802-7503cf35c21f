package com.jygjexp.jynx.tms.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.jygjexp.jynx.tms.constants.StoreEnums;
import com.jygjexp.jynx.tms.entity.TmsPayOrderEntity;
import com.jygjexp.jynx.tms.entity.TmsStoreOrderEntity;
import com.jygjexp.jynx.tms.service.TmsPayOrderService;
import com.jygjexp.jynx.tms.service.TmsStoreBalanceService;
import com.jygjexp.jynx.tms.service.TmsStoreOrderService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class TmsStoreOrderPayStatusSyncTask {

    private static final Logger logger = LoggerFactory.getLogger(TmsStoreOrderPayStatusSyncTask.class);
    private final TmsStoreOrderService storeOrderService;
    private final TmsPayOrderService payOrderService;
    private final TmsStoreBalanceService storeBalanceService;


    @SneakyThrows
    @XxlJob("tmsStoreOrderPayStatusSyncTask")
    public void executeTask() {
        // 一天内的数据
        DateTime nowDate = DateTime.now();
        DateTime dateTime = DateUtil.offsetDay(nowDate, -1);
        List<TmsStoreOrderEntity> storeOrders = storeOrderService.selectPendingPaymentStoreOrder(dateTime.toLocalDateTime());
        if(CollUtil.isNotEmpty(storeOrders)){
            List<String> entrustedOrderNumbers = storeOrders.stream().map(TmsStoreOrderEntity::getEntrustedOrderNumber).collect(Collectors.toList());
            if(logger.isInfoEnabled()){
                logger.info("[快递业务][在线支付] 定时任务执行,同步快递订单支付状态,orderIds:{}",entrustedOrderNumbers);
            }
            // 流水单号
            List<String> payOrderIds = storeOrders
                    .stream()
                    .filter(e-> ObjectUtil.isNotNull(e.getPayOrderId()))
                    .map(TmsStoreOrderEntity::getPayOrderId).collect(Collectors.toList());

            Map<String, TmsPayOrderEntity> mapByPayOrderIds = payOrderService.getMapByPayOrderIds(payOrderIds);
            for (TmsStoreOrderEntity storeOrder : storeOrders) {
                String payOrderId = storeOrder.getPayOrderId();
                Integer payStatus = storeOrder.getPayStatus();
                Long id = storeOrder.getId();
                String entrustedOrderNumber = storeOrder.getEntrustedOrderNumber();
                Long storeCustomerId = storeOrder.getStoreCustomerId();
                BigDecimal payAmount = storeOrder.getPayAmount();
                if(StrUtil.isBlank(payOrderId) || !StoreEnums.StoreOrder.PayStatus.PENDING_PAYMENT.getValue().equals(payStatus)){
                    continue;
                }
                TmsPayOrderEntity tmsPayOrderEntity = mapByPayOrderIds.get(payOrderId);
                if(null == tmsPayOrderEntity){
                    continue;
                }
                Integer status = tmsPayOrderEntity.getStatus();
                if(StoreEnums.PayOrder.PayStatus.SUCCESS.getValue().equals(status) ||
                        StoreEnums.PayOrder.PayStatus.COMPLETE.getValue().equals(status)){
                    TmsStoreOrderEntity updateEntity = new TmsStoreOrderEntity();
                    updateEntity.setId(id);
                    updateEntity.setPayStatus(StoreEnums.StoreOrder.PayStatus.PAYMENT.getValue());
                    storeOrderService.updateById(updateEntity);
                    storeOrderService.updatePayStatusByMainEntrustedNo(entrustedOrderNumber,StoreEnums.StoreOrder.PayStatus.PAYMENT.getValue());
                    // 生成账单
                    storeBalanceService.executePaymentOnline(storeCustomerId,payAmount,entrustedOrderNumber,payOrderId);
                }else if(!StoreEnums.PayOrder.PayStatus.PENDING.getValue().equals(status)){
                    TmsStoreOrderEntity updateEntity = new TmsStoreOrderEntity();
                    updateEntity.setId(id);
                    updateEntity.setPayStatus(StoreEnums.StoreOrder.PayStatus.PAYMENT_FAILED.getValue());
                    storeOrderService.updateById(updateEntity);
                    storeOrderService.updatePayStatusByMainEntrustedNo(entrustedOrderNumber,StoreEnums.StoreOrder.PayStatus.PAYMENT_FAILED.getValue());
                }
            }

        }
    }
}

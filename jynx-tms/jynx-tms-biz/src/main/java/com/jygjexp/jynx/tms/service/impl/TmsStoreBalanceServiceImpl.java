package com.jygjexp.jynx.tms.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jygjexp.jynx.common.security.util.SecurityUtils;
import com.jygjexp.jynx.tms.constants.StoreConstants;
import com.jygjexp.jynx.tms.constants.StoreEnums;
import com.jygjexp.jynx.tms.entity.*;
import com.jygjexp.jynx.tms.exception.CustomBusinessException;
import com.jygjexp.jynx.tms.mapper.TmsStoreBalanceMapper;
import com.jygjexp.jynx.tms.response.LocalizedR;
import com.jygjexp.jynx.tms.service.TmsStoreBalanceOfflineRechargeService;
import com.jygjexp.jynx.tms.service.TmsStoreBalanceRecordService;
import com.jygjexp.jynx.tms.service.TmsStoreBalanceService;
import com.jygjexp.jynx.tms.service.TmsStoreCustomerService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 门店余额表
 *
 * <AUTHOR>
 * @date 2025-07-14 18:49:59
 */
@Service
@RequiredArgsConstructor
public class TmsStoreBalanceServiceImpl extends ServiceImpl<TmsStoreBalanceMapper, TmsStoreBalanceEntity> implements TmsStoreBalanceService {
    private static final Logger logger = LoggerFactory.getLogger(TmsStoreBalanceServiceImpl.class);

    private final TmsStoreBalanceRecordService tmsStoreBalanceRecordService;
    private final TmsStoreBalanceOfflineRechargeService tmsStoreBalanceOfflineRechargeService;
    private final TmsStoreCustomerService tmsStoreCustomerService;

    /**
     * 线下充值
     *
     * @param offlineRecharge
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean offlineRecharge(Long id, TmsStoreBalanceOfflineRecharge offlineRecharge) {
        TmsStoreBalanceEntity entity = getOptById(id)
                .orElseThrow(() -> new CustomBusinessException("This record not exists."));

        Long storeCustomerId = entity.getStoreCustomerId();
        TmsStoreCustomerEntity storeCustomer = tmsStoreCustomerService.getById(storeCustomerId);
        Long storeId = storeCustomer.getStoreId();

        //流水记录
        TmsStoreBalanceRecordEntity balanceRecord = new TmsStoreBalanceRecordEntity();
        balanceRecord.setStoreBalanceId(entity.getId());
        balanceRecord.setStoreCustomerId(storeCustomerId);
        balanceRecord.setBeforeAmount(entity.getAmount());
        balanceRecord.setAfterAmount(entity.getAmount().add(offlineRecharge.getAmount()));
        balanceRecord.setChangeAmount(offlineRecharge.getAmount());
        balanceRecord.setType(5);
        balanceRecord.setSubType(0);
        balanceRecord.setStatus(0);
        balanceRecord.setRemark(offlineRecharge.getNotes());
        tmsStoreBalanceRecordService.save(balanceRecord);

        //流水号
        balanceRecord.setBusinessNumber(StrUtil.format("CZ{}{}", System.currentTimeMillis(), balanceRecord.getId()));
        tmsStoreBalanceRecordService.updateById(balanceRecord);

        //手工调帐记录
        offlineRecharge.setStoreId(storeId);
        offlineRecharge.setStoreCustomerId(storeCustomerId);
        offlineRecharge.setStoreBalanceRecordId(balanceRecord.getId());
        tmsStoreBalanceOfflineRechargeService.save(offlineRecharge);
        return true;
    }

    /**
     * 查询当前用户的余额
     *
     * @return
     */
    @Override
    public TmsStoreBalanceEntity getCurrent() {
        Long userId = SecurityUtils.getUser().getId();
        TmsStoreCustomerEntity storeCustomer = tmsStoreCustomerService.getStoreCustomerByUserId(userId);
        LambdaQueryWrapper<TmsStoreBalanceEntity> wrapper = Wrappers.lambdaQuery(TmsStoreBalanceEntity.class)
                .eq(TmsStoreBalanceEntity::getStoreCustomerId, storeCustomer.getId());
        return getOne(wrapper);
    }

    @Override
    public TmsStoreBalanceEntity getByStoreCustomerId(Long storeCustomerId) {
        if (null == storeCustomerId) {
            return null;
        }
        return baseMapper.getByStoreCustomerId(storeCustomerId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void executeOnlineOrderDeduction(TmsStoreBalanceEntity storeBalance, BigDecimal deductAmount, String entrustedOrderNumber) {
        if (null == storeBalance || null == deductAmount || StrUtil.isBlank(entrustedOrderNumber)) {
            throw new CustomBusinessException(LocalizedR.getMessage("tms.store.params.exception",null));
        }
        BigDecimal subtractAmount = storeBalance.getAmount().subtract(deductAmount);
        LambdaUpdateWrapper<TmsStoreBalanceEntity> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(TmsStoreBalanceEntity::getId, storeBalance.getId());
        updateWrapper.eq(TmsStoreBalanceEntity::getAmountHash, storeBalance.getAmountHash());
        updateWrapper.set(TmsStoreBalanceEntity::getAmount, subtractAmount);
        updateWrapper.set(TmsStoreBalanceEntity::getAmountHash, DigestUtil.sha256Hex(subtractAmount.toString()));
        int count = baseMapper.update(updateWrapper);
        if (count <= 0) {
            logger.error("[客户余额变更][金额哈希值不匹配]失败,storeBalance={}", storeBalance);
            throw new CustomBusinessException(LocalizedR.getMessage("tms.store.balance.amount.change.failed",null));
        }
        TmsStoreBalanceRecordEntity balanceRecord = new TmsStoreBalanceRecordEntity();
        balanceRecord.setStoreCustomerId(storeBalance.getStoreCustomerId());
        balanceRecord.setStoreBalanceId(storeBalance.getId());
        balanceRecord.setBeforeAmount(storeBalance.getAmount());
        balanceRecord.setAfterAmount(storeBalance.getAmount().subtract(deductAmount));
        balanceRecord.setChangeAmount(deductAmount);
        balanceRecord.setBusinessKey(entrustedOrderNumber);
        balanceRecord.setBusinessNumber(entrustedOrderNumber);
        balanceRecord.setType(StoreEnums.StoreBalanceRecord.BusinessType.ORDER_DEDUCT.getValue());
        balanceRecord.setSubType(StoreEnums.StoreBalanceRecord.SubType.CONSUME.getValue());
        balanceRecord.setStatus(StoreConstants.ONE);
        balanceRecord.setRemark(StoreEnums.StoreBalanceRecord.BusinessType.ORDER_DEDUCT.getName());
        tmsStoreBalanceRecordService.save(balanceRecord);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void executeOffLineOrderDeduction(TmsStoreBalanceEntity storeBalance, BigDecimal payAmount, String entrustedOrderNumber) {
        TmsStoreBalanceRecordEntity balanceRecord = new TmsStoreBalanceRecordEntity();
        balanceRecord.setStoreCustomerId(storeBalance.getStoreCustomerId());
        balanceRecord.setStoreBalanceId(storeBalance.getId());
        balanceRecord.setBeforeAmount(storeBalance.getAmount());
        balanceRecord.setAfterAmount(storeBalance.getAmount());
        balanceRecord.setChangeAmount(payAmount);
        balanceRecord.setBusinessKey(entrustedOrderNumber);
        balanceRecord.setBusinessNumber(IdUtil.getSnowflakeNextIdStr());
        balanceRecord.setType(StoreEnums.StoreBalanceRecord.BusinessType.OFFLINE_DEDUCT.getValue());
        balanceRecord.setSubType(StoreEnums.StoreBalanceRecord.SubType.CONSUME.getValue());
        balanceRecord.setStatus(StoreConstants.ONE);
        balanceRecord.setRemark(StoreEnums.StoreBalanceRecord.BusinessType.OFFLINE_DEDUCT.getName());
        tmsStoreBalanceRecordService.save(balanceRecord);
    }

    /**
     * 扣款
     *
     * @param storeCustomerId - 金额哈希
     * @param amount          - 流水单号
     */
    @Override
    public Boolean executeOrderDeduction(Long storeCustomerId, BigDecimal amount) {
        TmsStoreBalanceEntity storeBalance = getByStoreCustomerId(storeCustomerId);
        if (null == storeBalance || null == amount) {
            return false;
        }

        if (!StrUtil.equals(storeBalance.getAmountHash(), DigestUtil.sha256Hex(storeBalance.getAmount().toString()))) {
            logger.error("[客户余额变更][金额哈希值不匹配]失败,storeBalance={}",storeBalance);
            return false;
        }
        BigDecimal subtract = storeBalance.getAmount().subtract(amount);
        storeBalance.setAmount(subtract);
        storeBalance.setAmountHash(DigestUtil.sha256Hex(subtract.toString()));
        storeBalance.updateById();

        // 生成一条流水记录
        TmsStoreBalanceRecordEntity balanceRecord = new TmsStoreBalanceRecordEntity();
        balanceRecord.setStoreBalanceId(storeBalance.getId());
        balanceRecord.setStoreCustomerId(storeBalance.getStoreCustomerId());
        balanceRecord.setBeforeAmount(storeBalance.getAmount());
        balanceRecord.setAfterAmount(storeBalance.getAmount().subtract(amount));
        balanceRecord.setChangeAmount(amount);
        balanceRecord.setType(StoreEnums.StoreBalanceRecord.BusinessType.REFUND.getValue());
        balanceRecord.setSubType(StoreEnums.StoreBalanceRecord.SubType.RECHARGE.getValue());
        balanceRecord.setStatus(StoreConstants.ONE);
        balanceRecord.setRemark(StoreEnums.StoreBalanceRecord.BusinessType.RECHARGE_ONLINE.getName());
        tmsStoreBalanceRecordService.save(balanceRecord);

        balanceRecord.setBusinessNumber(StrUtil.format("CZ{}{}", System.currentTimeMillis(), balanceRecord.getId()));
        return balanceRecord.updateById();
    }

    @Override
    @Transactional
    public Boolean executeOrderAddition(Long storeCustomerId, BigDecimal amount, String businessNumber) {
        TmsStoreBalanceEntity storeBalance = getByStoreCustomerId(tmsStoreCustomerService.getStoreCustomerIdByUserId(storeCustomerId));
        if (null == storeBalance || null == amount) {
            return false;
        }

        if (!StrUtil.equals(storeBalance.getAmountHash(), DigestUtil.sha256Hex(storeBalance.getAmount().toString()))) {
            logger.error("[客户余额变更][金额哈希值不匹配]失败,storeBalance={}", storeBalance);
            return false;
        }
        BigDecimal add = storeBalance.getAmount().add(amount);
        storeBalance.setAmount(add);
        storeBalance.setAmountHash(DigestUtil.sha256Hex(add.toString()));
        storeBalance.updateById();

        // 生成一条流水记录
        TmsStoreBalanceRecordEntity balanceRecord = new TmsStoreBalanceRecordEntity();
        balanceRecord.setStoreBalanceId(storeBalance.getId());
        balanceRecord.setStoreCustomerId(storeBalance.getStoreCustomerId());
        balanceRecord.setBeforeAmount(storeBalance.getAmount());
        balanceRecord.setAfterAmount(storeBalance.getAmount().add(amount));
        balanceRecord.setChangeAmount(amount);
        balanceRecord.setType(StoreEnums.StoreBalanceRecord.BusinessType.RECHARGE_ONLINE.getValue());
        balanceRecord.setSubType(StoreEnums.StoreBalanceRecord.SubType.RECHARGE.getValue());
        balanceRecord.setStatus(StoreConstants.ONE);
        balanceRecord.setRemark(StoreEnums.StoreBalanceRecord.BusinessType.RECHARGE_ONLINE.getName());
        tmsStoreBalanceRecordService.save(balanceRecord);
        balanceRecord.setBusinessNumber(businessNumber);
        return balanceRecord.updateById();
    }

    /**
     * 初始化
     *
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean init() {
        remove(Wrappers.emptyWrapper());
        List<TmsStoreCustomerEntity> customers = tmsStoreCustomerService.list();
        List<TmsStoreBalanceEntity> collect = customers.stream()
                .filter(customer -> ObjUtil.equal(customer.getStatus(), 1))
                .map(TmsStoreCustomerEntity::getId)
                .map(id -> {
                    TmsStoreBalanceEntity entity = new TmsStoreBalanceEntity();
                    entity.setStoreCustomerId(id);
                    entity.setAmount(BigDecimal.ZERO);
                    entity.setAmountHash(DigestUtil.sha256Hex("0.00"));
                    return entity;
                })
                .collect(Collectors.toList());

        saveBatch(collect);
        return true;
    }

    @Override
    public Boolean compensate(Long storeCustomerId, BigDecimal amount) {
        TmsStoreBalanceEntity storeBalance = getByStoreCustomerId(storeCustomerId);
        if (null == storeBalance || null == amount) {
            throw new CustomBusinessException(LocalizedR.getMessage("tms.store.params.exception",null));
        }

        BigDecimal subtractAmount = storeBalance.getAmount().add(amount);

        LambdaUpdateWrapper<TmsStoreBalanceEntity> wrapper = Wrappers.lambdaUpdate(TmsStoreBalanceEntity.class)
                .eq(TmsStoreBalanceEntity::getStoreCustomerId, storeBalance.getStoreCustomerId())
                .eq(TmsStoreBalanceEntity::getAmountHash, storeBalance.getAmountHash())
                .set(TmsStoreBalanceEntity::getAmount, subtractAmount);
        int count = baseMapper.update(wrapper);
        if (count < 1) {
            logger.error("[客户余额变更][金额哈希值不匹配]失败,storeBalance={}", storeBalance);
            throw new CustomBusinessException(LocalizedR.getMessage("tms.store.balance.amount.change.failed",null));
        }

        // 生成一条流水记录
        TmsStoreBalanceRecordEntity balanceRecord = new TmsStoreBalanceRecordEntity();
        balanceRecord.setStoreBalanceId(storeBalance.getId());
        balanceRecord.setStoreCustomerId(storeBalance.getStoreCustomerId());
        balanceRecord.setBeforeAmount(storeBalance.getAmount());
        balanceRecord.setAfterAmount(storeBalance.getAmount().add(amount));
        balanceRecord.setChangeAmount(amount);
        balanceRecord.setType(StoreEnums.StoreBalanceRecord.BusinessType.REFUND.getValue());
        balanceRecord.setSubType(StoreEnums.StoreBalanceRecord.SubType.RECHARGE.getValue());
        balanceRecord.setStatus(StoreConstants.ONE);
        balanceRecord.setRemark(StoreEnums.StoreBalanceRecord.BusinessType.REFUND.getName());
        tmsStoreBalanceRecordService.save(balanceRecord);

        balanceRecord.setBusinessNumber(StrUtil.format("CZ{}{}", System.currentTimeMillis(), balanceRecord.getId()));
        balanceRecord.updateById();

        return true;
    }

    @Override
    public Long executeAdjustment(TmsStoreBalanceCustomerAdjustmentEntity balanceCustomerAdjustment) {
        TmsStoreBalanceEntity storeBalance = getByStoreCustomerId((balanceCustomerAdjustment.getStoreCustomerId()));
        if (null == storeBalance || null == balanceCustomerAdjustment.getAdjustmentAmount()) {
            return null;
        }
        if (!StrUtil.equals(storeBalance.getAmountHash(), DigestUtil.sha256Hex(storeBalance.getAmount().toString()))) {
            logger.error("[客户余额变更][金额哈希值不匹配]失败,storeBalance={}", storeBalance);
            return null;
        }
        BigDecimal originalAmount = storeBalance.getAmount();
        BigDecimal adjustmentAmount = balanceCustomerAdjustment.getAdjustmentAmount();
        BigDecimal newAmount;
        StoreEnums.StoreBalanceRecord.BusinessType businessType;

        // 根据调账类型处理金额和业务类型
        if (balanceCustomerAdjustment.getAdjustmentType() == 0) {
            newAmount = originalAmount.add(adjustmentAmount);
            businessType = StoreEnums.StoreBalanceRecord.BusinessType.INCREASE;
        } else {
            newAmount = originalAmount.subtract(adjustmentAmount);
            businessType = StoreEnums.StoreBalanceRecord.BusinessType.DECREASE;

            // 下调时检查余额是否足够
            if (newAmount.compareTo(BigDecimal.ZERO) < 0) {
                logger.error("[客户余额变更][余额不足]失败,当前余额={},调账金额={}", originalAmount, adjustmentAmount);
                throw new CustomBusinessException(LocalizedR.getMessage("tms.store.balance.insufficient",null));
            }
        }
        // 更新余额
        storeBalance.setAmount(newAmount);
        storeBalance.setAmountHash(DigestUtil.sha256Hex(newAmount.toString()));
        storeBalance.updateById();
        // 生成流水记录
        TmsStoreBalanceRecordEntity balanceRecord = new TmsStoreBalanceRecordEntity();
        balanceRecord.setStoreBalanceId(storeBalance.getId());
        balanceRecord.setStoreCustomerId(storeBalance.getStoreCustomerId());
        balanceRecord.setBeforeAmount(originalAmount);
        balanceRecord.setAfterAmount(newAmount);
        balanceRecord.setChangeAmount(adjustmentAmount);
        balanceRecord.setType(businessType.getValue());
        balanceRecord.setSubType(balanceCustomerAdjustment.getAdjustmentType() == 0 ? StoreEnums.StoreBalanceRecord.SubType.RECHARGE.getValue() : StoreEnums.StoreBalanceRecord.SubType.CONSUME.getValue());
        balanceRecord.setStatus(StoreConstants.ONE);
        balanceRecord.setRemark(businessType.getName());
        tmsStoreBalanceRecordService.save(balanceRecord);
        balanceRecord.setBusinessNumber(StrUtil.format("CZ{}{}", System.currentTimeMillis(), balanceRecord.getId()));
        balanceRecord.updateById();
        return balanceRecord.getStoreBalanceId();
    }

    @Override
    public void executePaymentOnline(Long storeCustomerId, BigDecimal amount, String entrustedOrderNumber,String businessNumber) {
        if(null == storeCustomerId || null == amount || StrUtil.isBlank(entrustedOrderNumber)) {
            return;
        }
        // 存在跳过




        TmsStoreBalanceEntity storeBalance = baseMapper.getByStoreCustomerId(storeCustomerId);
        TmsStoreBalanceRecordEntity balanceRecord = new TmsStoreBalanceRecordEntity();
        balanceRecord.setStoreCustomerId(storeBalance.getStoreCustomerId());
        balanceRecord.setStoreBalanceId(storeBalance.getId());
        balanceRecord.setBeforeAmount(storeBalance.getAmount());
        balanceRecord.setAfterAmount(storeBalance.getAmount());
        balanceRecord.setChangeAmount(amount);
        balanceRecord.setBusinessKey(entrustedOrderNumber);
        balanceRecord.setBusinessNumber(IdUtil.getSnowflakeNextIdStr());
        balanceRecord.setType(StoreEnums.StoreBalanceRecord.BusinessType.PAYMENT_ONLINE.getValue());
        balanceRecord.setSubType(StoreEnums.StoreBalanceRecord.SubType.CONSUME.getValue());
        balanceRecord.setStatus(StoreConstants.ONE);
        balanceRecord.setRemark(StoreEnums.StoreBalanceRecord.BusinessType.PAYMENT_ONLINE.getName());
        tmsStoreBalanceRecordService.save(balanceRecord);
    }

}

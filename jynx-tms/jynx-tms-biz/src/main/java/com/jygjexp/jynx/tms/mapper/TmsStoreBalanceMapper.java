package com.jygjexp.jynx.tms.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jygjexp.jynx.common.data.datascope.JynxBaseMapper;
import com.jygjexp.jynx.tms.entity.TmsStoreBalanceEntity;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface TmsStoreBalanceMapper extends JynxBaseMapper<TmsStoreBalanceEntity> {

    default TmsStoreBalanceEntity getByStoreCustomerId(Long storeCustomerId) {
        if(null == storeCustomerId){
            return null;
        }
        LambdaQueryWrapper<TmsStoreBalanceEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TmsStoreBalanceEntity::getStoreCustomerId, storeCustomerId);
        queryWrapper.last("for update");
        return selectOne(queryWrapper);
    }
}

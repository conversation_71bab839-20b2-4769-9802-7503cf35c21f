package com.jygjexp.jynx.tms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jygjexp.jynx.tms.dto.TmsStoreBalanceRecordExportDto;
import com.jygjexp.jynx.tms.entity.TmsStoreBalanceRecordEntity;
import com.jygjexp.jynx.tms.entity.TmsStoreCustomerEntity;
import com.jygjexp.jynx.tms.mapper.TmsStoreBalanceRecordMapper;
import com.jygjexp.jynx.tms.service.TmsStoreBalanceRecordService;
import com.jygjexp.jynx.tms.service.TmsStoreCustomerService;
import com.jygjexp.jynx.tms.vo.TmsStoreBalanceRecordPageVo;
import com.mongoplus.toolkit.CollUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 门店余额变更记录表
 *
 * <AUTHOR>
 * @date 2025-07-14 18:44:45
 */
@Service
@RequiredArgsConstructor
public class TmsStoreBalanceRecordServiceImpl extends ServiceImpl<TmsStoreBalanceRecordMapper, TmsStoreBalanceRecordEntity> implements TmsStoreBalanceRecordService {

    private final TmsStoreCustomerService tmsStoreCustomerService;

    /**
     * 分页
     *
     * @param vo
     * @return
     */
    @Override
    public Page<TmsStoreBalanceRecordEntity> search(TmsStoreBalanceRecordPageVo vo) {
        TmsStoreCustomerEntity currentCustomer = tmsStoreCustomerService.getCurrentCustomer();
        LambdaQueryWrapper<TmsStoreBalanceRecordEntity> wrapper = Wrappers.lambdaQuery(TmsStoreBalanceRecordEntity.class)
                .eq(TmsStoreBalanceRecordEntity::getStoreCustomerId, currentCustomer.getId())
                .eq(ObjUtil.isNotNull(vo.getType()), TmsStoreBalanceRecordEntity::getType, vo.getType())
                .eq(ObjUtil.isNotNull(vo.getSubType()), TmsStoreBalanceRecordEntity::getSubType, vo.getSubType())
                .eq(ObjUtil.isNotNull(vo.getStatus()), TmsStoreBalanceRecordEntity::getStatus, vo.getStatus())
                .eq(StrUtil.isNotBlank(vo.getBusinessKey()), TmsStoreBalanceRecordEntity::getBusinessKey, vo.getBusinessKey())
                .between(ObjUtil.isNotNull(vo.getCreateStartTime()) && ObjUtil.isNotNull(vo.getCreateEndTime()),
                        TmsStoreBalanceRecordEntity::getCreateTime, vo.getCreateStartTime(), vo.getCreateEndTime())
                .orderByDesc(TmsStoreBalanceRecordEntity::getId);
        return page(new Page<>(vo.getCurrent(), vo.getSize()), wrapper);
    }

    /**
     * 导出
     *
     * @param vo
     * @return
     */
    @Override
    public List<TmsStoreBalanceRecordExportDto> export(TmsStoreBalanceRecordPageVo vo) {
        TmsStoreCustomerEntity currentCustomer = tmsStoreCustomerService.getCurrentCustomer();
        LambdaQueryWrapper<TmsStoreBalanceRecordEntity> wrapper = Wrappers.lambdaQuery(TmsStoreBalanceRecordEntity.class)
                .eq(TmsStoreBalanceRecordEntity::getStoreCustomerId, currentCustomer.getId())
                .eq(ObjUtil.isNotNull(vo.getType()), TmsStoreBalanceRecordEntity::getType, vo.getType())
                .eq(ObjUtil.isNotNull(vo.getSubType()), TmsStoreBalanceRecordEntity::getSubType, vo.getSubType())
                .eq(ObjUtil.isNotNull(vo.getStatus()), TmsStoreBalanceRecordEntity::getStatus, vo.getStatus())
                .in(CollUtil.isNotEmpty(vo.getIds()), TmsStoreBalanceRecordEntity::getId, vo.getIds())
                .between(ObjUtil.isNotNull(vo.getCreateStartTime()) && ObjUtil.isNotNull(vo.getCreateEndTime()),
                        TmsStoreBalanceRecordEntity::getCreateTime, vo.getCreateStartTime(), vo.getCreateEndTime())
                .orderByDesc(TmsStoreBalanceRecordEntity::getId);
        return BeanUtil.copyToList(list(wrapper), TmsStoreBalanceRecordExportDto.class);
    }

    @Override
    public TmsStoreBalanceRecordEntity getByBusinessKey(String businessKey) {
        if(StrUtil.isBlank(businessKey)){
            return null;
        }
        LambdaQueryWrapper<TmsStoreBalanceRecordEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TmsStoreBalanceRecordEntity::getBusinessKey, businessKey);
        queryWrapper.last("limit 1");
        return baseMapper.selectOne(queryWrapper);
    }
}

package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsAgentEntity;
import com.jygjexp.jynx.tms.service.TmsAgentService;
import com.jygjexp.jynx.tms.vo.TmsAgentPageVo;
import com.jygjexp.jynx.tms.vo.TmsAgentUpdateVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 代理商
 *
 * <AUTHOR>
 * @date 2025-09-06 03:05:04
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsAgent")
@Tag(description = "tmsAgent", name = "代理商管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsAgentController {

    private final TmsAgentService tmsAgentService;

    /**
     * 分页查询
     *
     * @param page     分页对象
     * @param vo 代理商
     * @return
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @PostMapping("/search")
    @PreAuthorize("@pms.hasPermission('tms_tmsAgent_view')" )
    public R<Page<TmsAgentEntity>> search(@RequestBody TmsAgentPageVo vo) {
        return R.ok(tmsAgentService.search( vo));
    }

    /**
     * 通过id查询代理商
     *
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询", description = "通过id查询")
    @GetMapping("/{id}")
    @PreAuthorize("@pms.hasPermission('tms_tmsAgent_view')" )
    public R getById(@PathVariable("id") Long id) {
        return R.ok(tmsAgentService.getById(id));
    }

    /**
     * 新增代理商
     *
     * @param entity 代理商
     * @return R
     */
    @Operation(summary = "新增代理商", description = "新增代理商")
    @SysLog("新增代理商")
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsAgent_add')" )
    public R<Boolean> save(@RequestBody @Valid TmsAgentEntity entity) {
        return tmsAgentService.create(entity);
    }

    /**
     * 修改代理商
     *
     * @param vo 代理商
     * @return R
     */
    @Operation(summary = "修改代理商", description = "修改代理商")
    @SysLog("修改代理商")
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsAgent_edit')" )
    public R updateById(@RequestBody TmsAgentUpdateVo vo) {
        return R.ok(tmsAgentService.updated(vo));
    }

    /**
     * 启用
     * @param id
     * @return
     */
    @Operation(summary = "启用")
    @PutMapping("/{id}/enable")
    @PreAuthorize("@pms.hasPermission('tms_tmsAgent_edit')" )
    public R<Boolean> enable(@PathVariable Long id) {
        return tmsAgentService.enable(id);
    }

    /**
     * 禁用
     * @param id
     * @return
     */
    @Operation(summary = "禁用")
    @PutMapping("/{id}/disable")
    @PreAuthorize("@pms.hasPermission('tms_tmsAgent_edit')" )
    public R<Boolean> disable(@PathVariable Long id) {
        return tmsAgentService.disable(id);
    }

    /**
     * 通过id删除代理商
     *
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除代理商", description = "通过id删除代理商")
    @SysLog("通过id删除代理商")
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsAgent_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsAgentService.removeBatchByIds(CollUtil.toList(ids)));
    }
}
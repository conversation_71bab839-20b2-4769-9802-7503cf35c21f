package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.StrUtil;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.model.bo.PayParaVo;
import com.jygjexp.jynx.tms.utils.IOTPayQrOrder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 支付接口
 *
 * <AUTHOR>
 * @date 2025-08-05 16:18:31
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/postPay")
@Tag(description = "postPayServices", name = "支付接口")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsOnlinePayController {

    @Autowired
    private IOTPayQrOrder payQrOrder;

    /**
     * 支付接口
     *
     * @return R
     */
    @Operation(summary = "支付接口(单位为分)", description = "支付接口")
    @PostMapping("/pay")
    @PreAuthorize("@pms.hasPermission('tms_pay_edit','tms_store_pay_edit')")
    public R pay(@RequestBody PayParaVo payParaVo) {
        try {
            return payQrOrder.payCreate(payParaVo.getAmount(), payParaVo.getPayType(), payParaVo.getUserId());
        } catch (Exception e) {
            return R.failed("支付失败");
        }
    }


    /**
     * 支付接口(信用卡)
     *
     * @return R
     */
    @Operation(summary = "信用卡支付接口(单位为分)", description = "信用卡支付接口")
    @PostMapping("/creditPay")
    @PreAuthorize("@pms.hasPermission('tms_pay_edit','tms_store_pay_edit')")
    public R creditPay(@RequestBody PayParaVo payParaVo) {
        try {
            return payQrOrder.createCheckoutSession(payParaVo.getAmount(), payParaVo.getPayType(), payParaVo.getUserId(), payParaVo.getSuccessPage(),payParaVo.getCancelPage());
        } catch (Exception e) {
            return R.failed("支付失败");
        }
    }

    /**
     * 查询订单接口
     */

    @Operation(summary = "查询订单接口", description = "查询订单接口")
    @PostMapping("/query")
    @PreAuthorize("@pms.hasPermission('tms_pay_view','tms_store_pay_view')")
    public R query(String payOrderId) {
        if (StrUtil.isBlank(payOrderId)) {
            return R.failed();
        }
        return payQrOrder.query(payOrderId);
    }


    /**
     * 查询信用卡支付结果
     */

    @Operation(summary = "查询信用卡支付结果", description = "查询信用卡支付结果")
    @PostMapping("/queryCreditPay")
    @PreAuthorize("@pms.hasPermission('tms_pay_view','tms_store_pay_view')")
    public R queryCreditPay(String payOrderId) {
        if (StrUtil.isBlank(payOrderId)) {
            return R.failed();
        }
        return payQrOrder.queryCreditPay(payOrderId);
    }

    /**
     * 退款接口
     *
     * @return R
     */
    @Operation(summary = "退款接口(单位为分)", description = "退款接口")
    @RequestMapping("/refund")
    @PostMapping("@pms.hasPermission('tms_pay_edit','tms_store_pay_edit')")
    public R refund(@RequestBody PayParaVo payParaVo) {
        try {
            return payQrOrder.refundOrder(payParaVo.getAmount(), payParaVo.getPayOrderId(), payParaVo.getUserId());
        } catch (Exception e) {
            return R.failed("退款失败，请联系管理员");
        }
    }
}

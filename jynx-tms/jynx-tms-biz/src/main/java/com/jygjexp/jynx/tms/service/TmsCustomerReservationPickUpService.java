package com.jygjexp.jynx.tms.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.github.yulichang.base.MPJBaseService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.abo.TmsCustomerReservationPickUpAbo;
import com.jygjexp.jynx.tms.dto.AppScanningDTO;
import com.jygjexp.jynx.tms.dto.PickUpPathPlanDriverDto;
import com.jygjexp.jynx.tms.entity.TmsCustomerOrderEntity;
import com.jygjexp.jynx.tms.entity.TmsCustomerReservationPickUpEntity;
import com.jygjexp.jynx.tms.model.bo.QueryCondition;
import com.jygjexp.jynx.tms.qbo.TmsCustomerReservationPickUpQbo;
import com.jygjexp.jynx.tms.vo.TmsCustomerReservationPickUpVo;
import com.jygjexp.jynx.tms.vo.TmsPathPlanVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

public interface TmsCustomerReservationPickUpService extends MPJBaseService<TmsCustomerReservationPickUpEntity> {

    IPage<TmsCustomerReservationPickUpVo> pageData(QueryCondition<TmsCustomerReservationPickUpQbo> qbo);

    R saveData(TmsCustomerReservationPickUpAbo abo);

    TmsCustomerReservationPickUpVo getDataById(Long id);

    R updateDataById(TmsCustomerReservationPickUpEntity tmsCustomerReservationPickUp);

    TmsPathPlanVo pathPlanList(QueryCondition<TmsCustomerReservationPickUpQbo> qbo);

    void pathPlanDriver(PickUpPathPlanDriverDto dto);

    Boolean appScanning(AppScanningDTO dto);

    TmsCustomerReservationPickUpVo getDataByReservationNumber(String reservationNumber);

    List<TmsCustomerReservationPickUpVo> mapLabel(QueryCondition<TmsCustomerReservationPickUpQbo> qbo);

    R importData(MultipartFile file);

    R helpPlaceOrder(TmsCustomerReservationPickUpAbo abo);
}
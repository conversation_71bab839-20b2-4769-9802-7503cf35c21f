package com.jygjexp.jynx.tms.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.tms.dto.TmsStoreBalanceRecordExportDto;
import com.jygjexp.jynx.tms.entity.TmsStoreBalanceRecordEntity;
import com.jygjexp.jynx.tms.vo.TmsStoreBalanceRecordPageVo;

import java.util.List;

public interface TmsStoreBalanceRecordService extends IService<TmsStoreBalanceRecordEntity> {

    /**
     * 分页
     *
     * @param vo
     * @return
     */
    Page<TmsStoreBalanceRecordEntity> search(TmsStoreBalanceRecordPageVo vo);

    /**
     * 导出
     *
     * @param vo
     * @return
     */
    List<TmsStoreBalanceRecordExportDto> export(TmsStoreBalanceRecordPageVo vo);

    /**
     * 根据业务key查询数据
     */
    TmsStoreBalanceRecordEntity getByBusinessKey(String businessKey);

}

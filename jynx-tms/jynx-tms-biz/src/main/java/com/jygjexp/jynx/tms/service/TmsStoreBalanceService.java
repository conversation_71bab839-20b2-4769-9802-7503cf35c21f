package com.jygjexp.jynx.tms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.tms.entity.TmsStoreBalanceCustomerAdjustmentEntity;
import com.jygjexp.jynx.tms.entity.TmsStoreBalanceEntity;
import com.jygjexp.jynx.tms.entity.TmsStoreBalanceOfflineRecharge;

import java.math.BigDecimal;

public interface TmsStoreBalanceService extends IService<TmsStoreBalanceEntity> {

    /**
     * 线下充值
     *
     * @param offlineRecharge
     * @return
     */
    Boolean offlineRecharge(Long id, TmsStoreBalanceOfflineRecharge offlineRecharge);

    /**
     * 查询当前用户的余额
     *
     * @return
     */
    TmsStoreBalanceEntity getCurrent();

    /**
     * 根据门店客户id-获取余额
     *
     * @param storeCustomerId
     * @return
     */
    TmsStoreBalanceEntity getByStoreCustomerId(Long storeCustomerId);


    /**
     * 下单扣款-变更客户金额
     *
     * @param storeBalance         - 金额哈希
     * @param entrustedOrderNumber - 流水单号
     * @return
     */
    void executeOnlineOrderDeduction(TmsStoreBalanceEntity storeBalance, BigDecimal deductAmount, String entrustedOrderNumber);


    /**
     * 线下支付-不变更客户金额-生成记录
     *
     * @param storeBalance         - 金额哈希
     * @param entrustedOrderNumber - 流水单号
     * @return
     */
    void executeOffLineOrderDeduction(TmsStoreBalanceEntity storeBalance, BigDecimal payAmount, String entrustedOrderNumber);


    /**
     * 扣款-变更客户金额
     *
     * @param storeCustomerId         - 金额哈希
     * @param amount - 流水单号
     * @return
     */
    Boolean executeOrderDeduction(Long storeCustomerId, BigDecimal amount);

    /**
     * 线上充值-添加客户余额
     *
     * @param storeCustomerId
     * @param amount
     */
    Boolean executeOrderAddition(Long storeCustomerId, BigDecimal amount,String businessNumber);

    /**
     * 初始化所有客户的余额
     *
     * @return
     */
    Boolean init();

    /**
     * 赔付客户金额
     */
    Boolean compensate(Long storeCustomerId, BigDecimal amount);

    Long executeAdjustment(TmsStoreBalanceCustomerAdjustmentEntity tmsStoreBalanceCustomerAdjustment);

    /**
     * 在线支付 - 余额不变动-生成在线支付类型交易记录
     */
    void executePaymentOnline(Long storeCustomerId, BigDecimal amount,String entrustedOrderNumber,String businessNumber);
}

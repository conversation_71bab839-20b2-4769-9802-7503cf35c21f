package com.jygjexp.jynx.tms.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jygjexp.jynx.tms.entity.TmsPayOrderEntity;
import com.jygjexp.jynx.tms.mapper.TmsPayOrderMapper;
import com.jygjexp.jynx.tms.service.TmsPayOrderService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 用户充值支付订单表
 *
 * <AUTHOR>
 * @date 2025-07-25 17:23:22
 */
@Service
public class TmsPayOrderServiceImpl extends ServiceImpl<TmsPayOrderMapper, TmsPayOrderEntity> implements TmsPayOrderService {

    // 创建用户充值支付订单
    @Override
    public boolean createPayOrder(TmsPayOrderEntity payOrder) {
        return save(payOrder);
    }

    @Override
    public Map<String,TmsPayOrderEntity> getMapByPayOrderIds(List<String> payOrderIds) {
        if(CollUtil.isEmpty(payOrderIds)){
            return MapUtil.empty();
        }
        LambdaQueryWrapper<TmsPayOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(TmsPayOrderEntity::getPayOrderId, payOrderIds);
        List<TmsPayOrderEntity> payOrderEntities = baseMapper.selectList(queryWrapper);
        if(CollUtil.isEmpty(payOrderEntities)){
            return MapUtil.empty();
        }
        return payOrderEntities
                .stream()
                .collect(Collectors.toMap(TmsPayOrderEntity::getPayOrderId, Function.identity(),(v1, v2)->v1));
    }
}

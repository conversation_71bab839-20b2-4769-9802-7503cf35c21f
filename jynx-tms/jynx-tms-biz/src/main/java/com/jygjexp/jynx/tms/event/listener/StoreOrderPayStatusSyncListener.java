package com.jygjexp.jynx.tms.event.listener;

import com.jygjexp.jynx.tms.constants.StoreEnums;
import com.jygjexp.jynx.tms.entity.TmsStoreOrderEntity;
import com.jygjexp.jynx.tms.event.PayStatusEvent;
import com.jygjexp.jynx.tms.service.TmsStoreBalanceService;
import com.jygjexp.jynx.tms.service.TmsStoreOrderService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

@Service
@RequiredArgsConstructor
public class StoreOrderPayStatusSyncListener {
    private static final Logger logger = LoggerFactory.getLogger(StoreOrderPayStatusSyncListener.class);
    private final TmsStoreOrderService storeOrderService;
    private final TmsStoreBalanceService storeBalanceService;

    @EventListener(PayStatusEvent.class)
    public void handlerPayStatusSync(PayStatusEvent payStatusEvent) {
        String payOrderId = payStatusEvent.getPayOrderId();
        Integer payStatus = payStatusEvent.getPayStatus();
        if(logger.isInfoEnabled()){
            logger.info("[快递业务][在线支付] 回调事件处理,流水单号: {}", payOrderId);
        }
        TmsStoreOrderEntity storeOrderEntity = storeOrderService.getStoreOrderByPayOrderId(payOrderId);
        if(null != storeOrderEntity){
            Long storeCustomerId = storeOrderEntity.getStoreCustomerId();
            String entrustedOrderNumber = storeOrderEntity.getEntrustedOrderNumber();
            String payOrderIdStr = storeOrderEntity.getPayOrderId();
            BigDecimal payAmount = storeOrderEntity.getPayAmount();
            TmsStoreOrderEntity updateEntity = new TmsStoreOrderEntity();
            updateEntity.setId(storeOrderEntity.getId());
            updateEntity.setPayStatus(payStatus);
            storeOrderService.updateById(updateEntity);
            storeOrderService.updatePayStatusByMainEntrustedNo(entrustedOrderNumber,payStatus);
            // 生成账单: 支付成功产生账单
            if(StoreEnums.StoreOrder.PayStatus.PAYMENT.getValue().equals(payStatus)){
                storeBalanceService.executePaymentOnline(storeCustomerId,payAmount,entrustedOrderNumber,payOrderIdStr);
            }
            if(logger.isInfoEnabled()){
                logger.info("[快递业务][在线支付] 回调事件处理成功,流水单号: {}", payOrderId);
            }
        }
    }
}

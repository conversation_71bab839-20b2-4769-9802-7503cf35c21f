package com.jygjexp.jynx.tms.service;

import com.jygjexp.jynx.admin.api.dto.MessageEmailDTO;
import com.jygjexp.jynx.tms.entity.TmsCustomerReservationPickUpEntity;
import com.jygjexp.jynx.tms.utils.FeignUtil;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class EmailSendAsyncService {

    @Async
    public void sendPickupEmail(TmsCustomerReservationPickUpEntity result, List<String> emails) {
        MessageEmailDTO dto = new MessageEmailDTO();
        dto.setBizCode("EMAIL_FEISHU");

        dto.setMailAddress(emails);
        dto.setTitle("Neighboure Express—Your pickup has been scheduled /Votre cueillette a été planifiée");

        Map<String, String> htmlValues = new HashMap<>();
        htmlValues.put("customerName", result.getCustomerName());
        htmlValues.put("reservationNumber", result.getReservationNumber());
        htmlValues.put("pickupDate", String.valueOf(result.getPickupDate()));

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
        htmlValues.put("pickupStartTime", result.getStartPickupTime().format(formatter));
        htmlValues.put("pickupEndTime", result.getEndPickupTime().format(formatter));

        htmlValues.put("addressLine", String.format("%s,%s,%s", result.getAddressLine(), result.getPostalCode(), result.getCity()));
        htmlValues.put("weight", String.valueOf(result.getWeight()));
        htmlValues.put("packageNum", String.valueOf(result.getPackageNum()));

        dto.setHtmlValues(htmlValues);

        FeignUtil.sendEmail(dto);
    }

    @Async
    public void sendHelpPickup(TmsCustomerReservationPickUpEntity result, List<String> emails) {
        MessageEmailDTO dto = new MessageEmailDTO();
        dto.setBizCode("EMAIL_FEISHU");

        dto.setMailAddress(emails);
        dto.setTitle("Neighboure Express—Your pickup has been scheduled /Votre cueillette a été planifiée");

        Map<String, String> htmlValues = new HashMap<>();
        htmlValues.put("Hi" + result.getContactName(), "");
//        htmlValues.put("customerName", result.getCustomerName());
        htmlValues.put("reservationNumber", result.getReservationNumber());
        htmlValues.put("pickupDate", String.valueOf(result.getPickupDate()));

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
        htmlValues.put("pickupStartTime", result.getStartPickupTime().format(formatter));
        htmlValues.put("pickupEndTime", result.getEndPickupTime().format(formatter));

        htmlValues.put("addressLine", String.format("%s,%s,%s", result.getAddressLine(), result.getPostalCode(), result.getCity()));
        htmlValues.put("weight", String.valueOf(result.getWeight()));
        htmlValues.put("packageNum", String.valueOf(result.getPackageNum()));
        StringBuffer content = new StringBuffer();
        content.append("NB Packaging Guide\n");
        content.append("Follow these guidelines to prepare your shipment for pickup.\n");
        content.append("Download now\n");
        content.append("Need to make a change to your pickuprequest?\n");
        content.append("To make a change,including,pickup timeocation for pickup,shipment weight\n");
        content.append("andnumber of pieces,visit Purolator's virtuaassistant.\n");
        content.append("Modify pickup");
        htmlValues.put(content.toString(), "");
        dto.setHtmlValues(htmlValues);

        FeignUtil.sendEmail(dto);
    }

}

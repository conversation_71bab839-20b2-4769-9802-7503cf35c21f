package com.jygjexp.jynx.tms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jygjexp.jynx.admin.api.dto.UserDTO;
import com.jygjexp.jynx.admin.api.dto.UserInfo;
import com.jygjexp.jynx.admin.api.entity.SysRole;
import com.jygjexp.jynx.admin.api.entity.SysUser;
import com.jygjexp.jynx.admin.api.feign.RemoteRoleService;
import com.jygjexp.jynx.admin.api.feign.RemoteUserService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.core.util.RetOps;
import com.jygjexp.jynx.tms.entity.TmsAgentEntity;
import com.jygjexp.jynx.tms.exception.CustomBusinessException;
import com.jygjexp.jynx.tms.mapper.TmsAgentMapper;
import com.jygjexp.jynx.tms.service.TmsAgentService;
import com.jygjexp.jynx.tms.vo.TmsAgentPageVo;
import com.jygjexp.jynx.tms.vo.TmsAgentUpdateVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * 代理商
 *
 * <AUTHOR>
 * @date 2025-09-06 03:05:04
 */
@Service
@RequiredArgsConstructor
public class TmsAgentServiceImpl extends ServiceImpl<TmsAgentMapper, TmsAgentEntity> implements TmsAgentService {

    private final RemoteUserService remoteUserService;
    private final RemoteRoleService remoteRoleService;

    /**
     * 分页
     *
     * @param vo
     * @return
     */
    @Override
    public Page<TmsAgentEntity> search(TmsAgentPageVo vo) {
        LambdaQueryWrapper<TmsAgentEntity> wrapper = Wrappers.lambdaQuery(TmsAgentEntity.class)
                .eq(ObjUtil.isNotNull(vo.getStatus()), TmsAgentEntity::getStatus, vo.getStatus())
                .like(StrUtil.isNotBlank(vo.getCode()), TmsAgentEntity::getCode, vo.getCode())
                .like(StrUtil.isNotBlank(vo.getName()), TmsAgentEntity::getName, vo.getName())
                .like(StrUtil.isNotBlank(vo.getLinkman()), TmsAgentEntity::getLinkman, vo.getLinkman())
                .like(StrUtil.isNotBlank(vo.getPhone()), TmsAgentEntity::getPhone, vo.getPhone())
                .between(ObjUtil.isNotEmpty(vo.getCreateStartTime()) && ObjUtil.isNotEmpty(vo.getCreateEndTime()),
                        TmsAgentEntity::getCreateTime, vo.getCreateStartTime(), vo.getCreateEndTime());
        return page(new Page<>(vo.getCurrent(), vo.getSize()), wrapper);
    }

    /**
     * 创建代理商
     *
     * @param entity
     * @return
     */
    @Override
    //@GlobalTransactional
    public R<Boolean> create(TmsAgentEntity entity) {
        boolean codeExists = exists(Wrappers.lambdaQuery(TmsAgentEntity.class)
                .eq(TmsAgentEntity::getCode, entity.getCode()));
        if (codeExists) {
            R.failed(StrUtil.format("Agent code: {} exists", entity.getCode()));
        }

        boolean nameExists = exists(Wrappers.lambdaQuery(TmsAgentEntity.class)
                .eq(TmsAgentEntity::getName, entity.getName()));
        if (nameExists) {
            R.failed(StrUtil.format("Agent name: {} exists", entity.getName()));
        }

        boolean phoneExists = exists(Wrappers.lambdaQuery(TmsAgentEntity.class)
                .eq(TmsAgentEntity::getPhone, entity.getPhone()));
        if (phoneExists) {
            R.failed(StrUtil.format("Agent phone: {} exists", entity.getPhone()));
        }

        R<List<SysRole>> allRole = remoteRoleService.getAllRole();
        List<SysRole> roles = allRole.getData();
        Long tmsAgent = roles.stream()
                .filter(role -> StrUtil.equals(role.getRoleCode(), "TMS_AGENT"))
                .map(SysRole::getRoleId)
                .findFirst()
                .get();

        //注册用户
        UserDTO userDTO = new UserDTO();
        userDTO.setUsername(entity.getName());
        userDTO.setPhone(entity.getPhone());
        userDTO.setRole(ListUtil.of(tmsAgent));
        userDTO.setPassword("123456");
        R<Boolean> result = remoteUserService.registerUser(userDTO);
        if (!RetOps.of(result).isSuccess()) {
            return R.failed(result.getMsg());
        }

        //用户id
        Optional<Long> userId = RetOps.of(remoteUserService.info(entity.getName()))
                .getData()
                .map(UserInfo::getSysUser)
                .map(SysUser::getUserId);
        entity.setUserId(userId.get());

        save(entity);
        return R.ok();
    }

    /**
     * 编辑
     *
     * @param vo
     * @return
     */
    @Override
    public Object updated(TmsAgentUpdateVo vo) {
        TmsAgentEntity entity = BeanUtil.copyProperties(vo, TmsAgentEntity.class);
        return updateById(entity);
    }

    /**
     * 启用
     *
     * @param id
     * @return
     */
    @Override
    //@GlobalTransactional
    public R<Boolean> enable(Long id) {
        TmsAgentEntity entity = getOptById(id)
                .orElseThrow(() -> new CustomBusinessException("Agent not exists."));
        entity.setStatus(1);
        updateById(entity);
        return remoteUserService.userEnables(ListUtil.of(entity.getUserId()));
    }

    /**
     * 禁用
     *
     * @param id
     * @return
     */
    @Override
    //@GlobalTransactional
    public R<Boolean> disable(Long id) {
        TmsAgentEntity entity = getOptById(id)
                .orElseThrow(() -> new CustomBusinessException("Agent not exists."));
        entity.setStatus(0);
        updateById(entity);
        return remoteUserService.lockUser(entity.getName());
    }
}
package com.jygjexp.jynx.tms.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.entity.TmsPayOrderEntity;
import com.jygjexp.jynx.tms.service.TmsOrderLogService;
import com.jygjexp.jynx.tms.service.TmsPayOrderService;
import com.jygjexp.jynx.tms.service.TmsStoreBalanceService;
import com.jygjexp.jynx.tms.vo.PaymentResponse;
import com.jygjexp.jynx.tms.vo.PaymentResponseVo;
import com.jygjexp.jynx.tms.vo.RetDetail;
import com.stripe.Stripe;
import com.stripe.exception.StripeException;
import com.stripe.model.checkout.Session;
import com.stripe.net.RequestOptions;
import com.stripe.param.checkout.SessionCreateParams;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.security.MessageDigest;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;


/**
 * IOT支付接口
 */
@Service
public class IOTPayQrOrder {


    @Autowired
    private TmsOrderLogService tmsOrderLogService;
    @Autowired
    private TmsPayOrderService tmsPayOrderService;
    @Autowired
    private TmsStoreBalanceService tmsStoreBalanceService;
    @Autowired
    private OkHttpClient client;
    @Autowired
    private ObjectMapper objectMapper;

    private static final String MID = "11112637";
    private static final String KEY = "3CGmY08vbeBjer2iNamkrelswYQtyUzr";
    private static final String USERNAME = "Neighbexp";


    /**
     * 创建支付订单
     *
     * @param amount
     * @param payType
     * @param userId
     * @throws Exception
     */
    public R payCreate(BigDecimal amount, String payType, Long userId) throws Exception {
        if (!isAtMostTwoDecimalPlaces(amount)) {
            return R.failed("金额不能小于等于0，不能超过两位小数");
        }
        //构建请求参数
        String url = "https://api.iotpaycloud.com/v1/create_order";
        String orderNo = generateId(userId.toString());
        Map<String, Object> map = new HashMap<>();
        map.put("mchId", MID);
        map.put("mchOrderNo", orderNo);
        map.put("channelId", payType);
        map.put("currency", "CAD");
        map.put("amount", amount.multiply(BigDecimal.valueOf(100)).intValue());
        map.put("clientIp", "127.0.0.1");
        map.put("device", "WEB");
        map.put("notifyUrl", "http://api.neighbourexpress.com/zt/api/payment/callback");
        map.put("subject", "积分");
        map.put("body", "积分充值");
        Map<String, Object> extra = new HashMap<>();
        extra.put("productId", "");
        map.put("extra", extra.toString());
        String sign = getSign(map, KEY);
        map.put("sign", sign);
        map.put("subject", URLEncoder.encode((String) map.get("subject"), "UTF-8"));
        map.put("body", URLEncoder.encode((String) map.get("body"), "UTF-8"));
        String params = JSON.toJSONString(map);
        String fullParams = "params=" + params;
        RequestBody formBody = RequestBody.create(fullParams, MediaType.get("application/x-www-form-urlencoded; charset=utf-8"));
        Request request = new Request.Builder()
                .url(url)
                .post(formBody)
                .build();
        tmsOrderLogService.saveLog(orderNo, 1001, formBody.toString(), userId.toString());
        //返回响应结果
        try (Response response = client.newCall(request).execute()) {
            if (response.body() != null) {
                String result = response.body().string();
                PaymentResponseVo paymentResponse = objectMapper.readValue(result, PaymentResponseVo.class);
                tmsPayOrderService.createPayOrder(new TmsPayOrderEntity(paymentResponse.getMchOrderNo(), userId, amount, payType, paymentResponse.getPayOrderId()));
                tmsOrderLogService.saveLog(orderNo, 1001, result.toString(), userId.toString());
                return R.ok(result);
            } else {
                return R.failed("响应为空");
            }
        } catch (IOException e) {
            tmsOrderLogService.saveLog(orderNo, 1001, "请求支付异常", userId.toString());
            return R.failed("请求失败: " + e.getMessage());
        }
    }


    //信用卡支付
    public R createCheckoutSession(BigDecimal amount, String payType, Long userId, String successPage,String cancelPage) throws Exception {

        String orderNo = generateId(userId.toString());
//
//        String successUrl = "https://www.neighbourexpress.com/monery/balance-mgm/index?flag=1&orderNo="+orderNo;
//        String cancelUrl = "https://www.neighbourexpress.com/monery/balance-mgm/index?flag=0&orderNo="+orderNo;
//        if ("manage".equals(currentPage )){
//            successUrl = "https://www.neighbourexpress.com/order-mgn/store-list/index?flag=1&orderNo="+orderNo;
//            cancelUrl = "https://www.neighbourexpress.com/order-mgn/store-list/index?flag=0&orderNo="+orderNo;
//        }

         // Stripe.apiKey = "sk_test_51Ru2EiF4XB9fBXWCrB1D1Vfv7dXBRktX3BH6ZPHZ6tGtRfPIjixguSBxxfl9BhlcuqTujAzu5sxUmmnO4PlgWQon00yVOOCAJw";
        Stripe.apiKey = "***********************************************************************************************************";
        SessionCreateParams params = SessionCreateParams.builder()
                .setMode(SessionCreateParams.Mode.PAYMENT)
                .setSuccessUrl(successPage)
                .setCancelUrl(cancelPage)
                .putMetadata("order_id", orderNo)
                .putMetadata("customer_id", userId.toString())
                .addLineItem(SessionCreateParams.LineItem.builder()
                        .setQuantity(1L)
                        .setPriceData(SessionCreateParams.LineItem.PriceData.builder()
                                .setCurrency("cad")
                                .setUnitAmount(amount.multiply(BigDecimal.valueOf(100)).longValue())
                                .setProductData(SessionCreateParams.LineItem.PriceData.ProductData.builder()
                                        .setName("积分")
                                        .build())
                                .build())
                        .build())
                .build();

        RequestOptions requestOptions = RequestOptions.builder()
                .setIdempotencyKey(orderNo)
                .build();

        try {
            Session session = Session.create(params, requestOptions);
            Map<String, String> responseData = new HashMap<>();
            responseData.put("checkoutUrl", session.getUrl());
            responseData.put("orderNo", orderNo);
            responseData.put("payment_intent_client_secret", session.getClientSecret());
            tmsPayOrderService.createPayOrder(new TmsPayOrderEntity(orderNo, userId, amount, payType, orderNo));
            return R.ok(responseData);
        } catch (StripeException e) {
            e.printStackTrace();
            return R.failed();
        }
    }

    /**
     * 退款
     *
     * @param amount
     * @param payOrderId
     */
    public R refundOrder(BigDecimal amount, String payOrderId, Long userId) throws Exception {
        String url = "https://api.iotpaycloud.com/v1/refund_order";

        // 构建参数
        Map<String, Object> map = new HashMap<>();
        map.put("mchId", MID);
        map.put("mchRefundNo", userId + System.currentTimeMillis());
        map.put("refundAmount", amount.multiply(BigDecimal.valueOf(100)).intValue()); // 单位为分
        map.put("clientIp", "127.0.0.1");
        map.put("device", "WEB");
        map.put("loginName", USERNAME);
        map.put("payOrderId", payOrderId);

        // 生成签名
        String sign = getSign(map, KEY);
        map.put("sign", sign);

        // 构建 JSON 字符串并封装为 x-www-form-urlencoded 格式
        String params = JSON.toJSONString(map);
        String fullParams = "params=" + params;

        // 创建请求
        RequestBody formBody = RequestBody.create(fullParams, MediaType.get("application/x-www-form-urlencoded; charset=utf-8"));
        Request request = new Request.Builder()
                .url(url)
                .post(formBody)
                .build();

        // 同步执行
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                System.out.println("HTTP请求失败，code=" + response.code());
                return R.failed();
            }

            String responseBody = response.body().string();
            System.out.println("退款返回内容：" + responseBody);

            // 解析返回 JSON，判断是否成功
            JSONObject json = JSON.parseObject(responseBody);
            String resCode = json.getString("resCode");
            String retCode = json.getString("retCode");

            if ("SUCCESS".equalsIgnoreCase(resCode) && "SUCCESS".equalsIgnoreCase(retCode)) {
                System.out.println("退款成功");
                //扣减用户余额
                try {
                    Boolean flag = tmsStoreBalanceService.executeOrderDeduction(userId, amount.multiply(BigDecimal.valueOf(100)));
                    return flag ? R.ok() : R.failed();
                } catch (Exception e) {
                    return R.failed();
                }
            } else {
                System.out.println("退款失败，resCode: " + resCode + ", retCode: " + retCode);
                return R.failed(responseBody);
            }
        }
    }


    /**
     * 查询订单
     *
     * @param payOrderId
     * @return
     */
    public R query(String payOrderId) {
        try {
            // 构建参数 Map（不含 sign）
            Map<String, Object> params = new HashMap<>();
            params.put("mchId", MID);
            params.put("payOrderId", payOrderId);

            // 生成签名
            String sign = getSign(params, KEY);
            params.put("sign", sign);

            // 将整个 params Map 序列化为 JSON 字符串
            String jsonParams = JSON.toJSONString(params);

            // 构建 formBody，参数名是 "params"，值是 JSON 字符串
            FormBody formBody = new FormBody.Builder()
                    .add("params", jsonParams)
                    .build();

            // 构建请求
            Request request = new Request.Builder()
                    .url("https://api.iotpaycloud.com/v1/query_order")
                    .addHeader("Content-Type", "application/x-www-form-urlencoded")
                    .post(formBody)
                    .build();

            // 创建 OkHttpClient
            try (Response response = client.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String result = response.body().string();
                    PaymentResponse paymentResponse = objectMapper.readValue(result, PaymentResponse.class);
                    if ("SUCCESS".equals(paymentResponse.getResCode()) && "SUCCESS".equals(paymentResponse.getRetCode())) {
                        payResultHandel(paymentResponse.getRetDetail());

                    }
                    return R.ok(result);
                } else {
                    return R.failed();
                }
            }
        } catch (Exception e) {
            System.err.println("异常发生: " + e.getMessage());
            e.printStackTrace();
        }
        return R.failed();
    }

    //查询信用卡支付结果
    public R queryCreditPay(String payOrderId) {
        LambdaQueryWrapper<TmsPayOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TmsPayOrderEntity::getPayOrderId, payOrderId)
                .eq(TmsPayOrderEntity::getStatus, 2);
        //查询该记录是否存在
        long count = tmsPayOrderService.count(wrapper);
        return count > 0 ? R.ok() : R.failed();
    }

    /**
     * 签名
     *
     * @param map
     * @param key
     * @return
     */
    public static String getSign(Map<String, Object> map, String key) {
        ArrayList<String> list = new ArrayList<>();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            if (entry.getValue() != null && !"".equals(entry.getValue()) && !"sign".equals(entry.getKey())) {
                list.add(entry.getKey() + "=" + entry.getValue() + "&");
            }
        }
        String[] arrayToSort = list.toArray(new String[0]);
        Arrays.sort(arrayToSort, String.CASE_INSENSITIVE_ORDER);
        StringBuilder sb = new StringBuilder();
        for (String s : arrayToSort) sb.append(s);
        sb.append("key=").append(key);
        return md5(sb.toString(), "UTF-8").toUpperCase();
    }

    public static String md5(String input, String charset) {
        try {
            byte[] data = input.getBytes(charset);
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(data);
            StringBuilder hex = new StringBuilder();
            for (byte b : digest) {
                String hexStr = Integer.toHexString(b & 0xff);
                if (hexStr.length() == 1) hex.append('0');
                hex.append(hexStr);
            }
            return hex.toString();
        } catch (Exception e) {
            throw new RuntimeException("MD5 签名失败", e);
        }
    }

    // 判断金额是否最多两位小数
    public static boolean isAtMostTwoDecimalPlaces(BigDecimal amount) {
        // 非空判断 + 金额必须大于 0 + 最多两位小数
        return amount != null
                && amount.compareTo(BigDecimal.ZERO) > 0
                && amount.scale() <= 2;
    }

    //处理查询结果
    public Boolean payResultHandel(RetDetail request) {
        if (request.getStatus() != 2 && request.getStatus() != 3) {
            return false;
        }
        Long userId = Long.valueOf(request.getMchOrderNo().split("_")[0]);
        LambdaUpdateWrapper<TmsPayOrderEntity> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(TmsPayOrderEntity::getPayOrderId, request.getPayOrderId())
                .eq(TmsPayOrderEntity::getOrderNo, request.getMchOrderNo())
                .eq(TmsPayOrderEntity::getStatus, 0)
                .isNull(TmsPayOrderEntity::getNotifyTime)
                .set(TmsPayOrderEntity::getStatus, request.getStatus())
                .set(TmsPayOrderEntity::getNotifyTime, LocalDateTime.now());

        boolean updated = tmsPayOrderService.update(updateWrapper);
        if (!updated) {
            // 已处理或不符合条件，直接返回 false
            return false;
        }

        // 2. 只有成功更新的线程才能继续
        BigDecimal amount = BigDecimal.valueOf(request.getAmount())
                .divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);

        return tmsStoreBalanceService.executeOrderAddition(userId, amount, request.getPayOrderId());
    }

    //因为客户回调接口太垃圾，需要轮训+定时查询支付结果
    public void payResultQuery() {
        //查询最近三十分钟内创建的且没支付成功的订单
        LambdaUpdateWrapper<TmsPayOrderEntity> query = new LambdaUpdateWrapper<>();
        query.gt(TmsPayOrderEntity::getCreateTime, LocalDateTime.now().minusMinutes(30))
                .eq(TmsPayOrderEntity::getStatus, 0)
                .isNull(TmsPayOrderEntity::getNotifyTime);
        List<TmsPayOrderEntity> list = tmsPayOrderService.list(query);
        for (TmsPayOrderEntity tmsPayOrderEntity : list) {
            query(tmsPayOrderEntity.getPayOrderId());
        }
    }

    /**
     * 生成：用户ID + 毫秒时间戳 + 3位随机数
     * @return 唯一订单号字符串
     */
    public  String generateId(String userId) {
        long timestamp = System.currentTimeMillis();
        int randomNum = ThreadLocalRandom.current().nextInt(100, 1000);
        return userId+"_"+timestamp + randomNum;
    }


}

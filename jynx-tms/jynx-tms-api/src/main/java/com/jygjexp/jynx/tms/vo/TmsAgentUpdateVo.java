package com.jygjexp.jynx.tms.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;


/**
 * 代理商
 *
 * <AUTHOR>
 * @date 2025-09-06 03:05:04
 */
@Data
public class TmsAgentUpdateVo {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 联系人
     */
    @Schema(description = "联系人")
    private String linkman;

    /**
     * 联系人电话
     */
    @Schema(description = "联系人电话")
    private String phone;

    /**
     * 代理商等级
     */
    @NotNull(message = "agent level cannot empty")
    @Schema(description = "代理商等级")
    private Integer level;

    /**
     * 状态 0:禁用;1:启用
     */
    @Schema(description = "状态 0:禁用;1:启用")
    private Integer status;
}
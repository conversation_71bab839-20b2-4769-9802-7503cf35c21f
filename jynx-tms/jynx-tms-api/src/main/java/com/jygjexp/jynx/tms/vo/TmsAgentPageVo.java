package com.jygjexp.jynx.tms.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;


/**
 * 代理商
 *
 * <AUTHOR>
 * @date 2025-09-06 03:05:04
 */
@Data
public class TmsAgentPageVo {
    /**
     * 当前页
     */
    @Schema(description = "当前页")
    private Integer current;

    /**
     * 页大小
     */
    @Schema(description = "页大小")
    private Integer size;

    /**
     * 代理商编码
     */
    @Schema(description = "代理商编码")
    private String code;

    /**
     * 代理商名称
     */
    @Schema(description = "代理商名称")
    private String name;

    /**
     * 联系人
     */
    @Schema(description = "联系人")
    private String linkman;

    /**
     * 联系人电话
     */
    @Schema(description = "联系人电话")
    private String phone;

    /**
     * 代理商等级
     */
    @Schema(description = "代理商等级    ")
    private Integer level;

    /**
     * 状态 0:禁用;1:启用
     */
    @Schema(description = "状态 0:禁用;1:启用")
    private Integer status;

    /**
     * 创建开始时间
     */
    @Schema(description = "创建开始时间")
    private LocalDateTime createStartTime;

    /**
     * 创建结束时间
     */
    @Schema(description = "创建结束时间")
    private LocalDateTime createEndTime;
}
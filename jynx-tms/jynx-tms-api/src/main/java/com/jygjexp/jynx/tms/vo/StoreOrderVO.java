package com.jygjexp.jynx.tms.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
public class StoreOrderVO {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "门店客户id")
    private Long storeCustomerId;

    @Schema(description = "订单号")
    private String entrustedOrderNumber;

    @Schema(description = "订单状态")
    private Integer orderStatus;

    @Schema(description = "支付状态")
    private Integer payStatus;

    @Schema(description = "流水单号")
    private String payOrderId;

    @Schema(description = "始发地")
    private String shipperOrigin;

    @Schema(description = "目的地")
    private String receiverDest;

    @Schema(description = "发货-联系人")
    private String shipperName;

    @Schema(description = "发货-联系电话")
    private String shipperPhone;

    @Schema(description = "发货-地址")
    private String shipperAddress;

    @Schema(description = "收货-联系人")
    private String receiverName;

    @Schema(description = "收货-联系电话")
    private String receiverPhone;

    @Schema(description = "收货-地址")
    private String receiverAddress;
//
//    @Schema(description = "货物数量")
//    private Integer storeGoods;

    @Schema(description = "单位:0公制 1:英制")
    private Integer unitType;

    @Schema(description = "货物数量")
    private Integer totalCounts;

    @Schema(description = "总重量")
    private BigDecimal totalWeight;

    @Schema(description = "总体积")
    private BigDecimal totalVolume;

    @Schema(description = "运费金额")
    private BigDecimal totalFreightAmount;

    @Schema(description = "是否POD")
    private Integer podSign;

    @Schema(description = "是否保险")
    private Integer insuranceSign;

    @Schema(description = "是否带磁带电")
    private Integer batterySign;

    @Schema(description = "服务商code")
    private String providerCode;

    @Schema(description = "服务商")
    private String providerName;

    @Schema(description = "运输时效")
    private String providerTransportTime;

    @Schema(description = "盲盒code")
    private String boxCode;

    @Schema(description = "盲盒名称")
    private String boxName;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "下单时间")
    private LocalDateTime createTime;

    @Schema(description = "打印凭证")
    private Integer printStatus;

    @Schema(description = "打印凭证时间")
    private LocalDateTime printTime;

    @Schema(description = "客户名称")
    private String customerName;

    @Schema(description = "门店名称")
    private String storeName;

    @Schema(description = "核销状态")
    private Integer writeOffFlag;

    @Schema(description = "成本价")
    private BigDecimal baseFreightAmount;

    @Schema(description = "差价")
    private BigDecimal diffAmount;

    @Schema(description = "推单类型")
    private Integer externalType;

    @Schema(description = "面单地址")
    private String labelPath;

    @Schema(description = "跟踪订单")
    private String externalOrderNumber;

    @Schema(description = "支付运费金额")
    private BigDecimal payAmount;
}

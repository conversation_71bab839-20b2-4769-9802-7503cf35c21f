package com.jygjexp.jynx.tms.dto.app;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ListDeliveryOrderDTO {
    @Schema(description = "订单状态 12：查询配送中 6：查询已完成 10：查询配送失败")
    private Integer status;
    @Schema(description = "司机id")
    private Long driverId;
}

package com.jygjexp.jynx.tms.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.jygjexp.jynx.tms.annotation.ConvertType;
import com.jygjexp.jynx.tms.utils.IntegerDictsConverter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Getter
@Setter
@HeadRowHeight(60)
@HeadFontStyle(fontHeightInPoints = 12)
@NoArgsConstructor
public class StoreOrderExcelVO {

    @ExcelProperty(value = "entrustedOrderNumber(订单号)")
    @Schema(description = "订单号")
    private String entrustedOrderNumber;

    @ExcelProperty(value = "externalOrderNumber(跟踪单号)")
    @Schema(description = "跟踪单号")
    private String externalOrderNumber;

    @ConvertType("storeOrderStatus")
    @ExcelProperty(value = "orderStatus(订单状态)",converter = IntegerDictsConverter.class)
    @Schema(description = "订单状态")
    private Integer orderStatus;

    @ConvertType("storeOrderPayStatus")
    @ExcelProperty(value = "payStatus(支付状态)",converter = IntegerDictsConverter.class)
    @Schema(description = "支付状态")
    private Integer payStatus;

    @ExcelProperty(value = "shipperOrigin(始发地)")
    @Schema(description = "始发地")
    private String shipperOrigin;

    @ExcelProperty(value = "receiverDest(目的地)")
    @Schema(description = "目的地")
    private String receiverDest;

    @ExcelProperty(value = "shipperName(发货联系人)")
    @Schema(description = "发货-联系人")
    private String shipperName;

    @ExcelProperty(value = "shipperPhone(发货人联系电话)")
    @Schema(description = "发货-联系电话")
    private String shipperPhone;

    @ExcelProperty(value = "shipperAddress(发货地址)")
    @Schema(description = "发货-地址")
    private String shipperAddress;

    @ExcelProperty(value = "receiverName(收货联系人)")
    @Schema(description = "收货-联系人")
    private String receiverName;

    @ExcelProperty(value = "receiverPhone(收货人联系电话)")
    @Schema(description = "收货-联系电话")
    private String receiverPhone;

    @ExcelProperty(value = "receiverAddress(收货地址)")
    @Schema(description = "收货-地址")
    private String receiverAddress;

    @ExcelProperty(value = "storeGoodsCounts(货物数量)")
    @Schema(description = "货物数量")
    private Integer totalCounts;

    @ExcelProperty(value = "totalWeight(总重量(kg))")
    @Schema(description = "总重量")
    private BigDecimal totalWeight;

    @ExcelProperty(value = "totalVolume(总体积(m³))")
    @Schema(description = "总体积")
    private BigDecimal totalVolume;

    @ExcelProperty(value = "freightAmount(运费金额(C$))")
    @Schema(description = "总体积")
    private BigDecimal totalFreightAmount;

    @ExcelProperty(value = "providerName(服务商)")
    @Schema(description = "服务商")
    private String providerName;

    @Schema(description = "盲盒code")
    private String boxCode;

    @Schema(description = "盲盒名称")
    private String boxName;


    @ExcelProperty(value = "remark(备注)")
    @Schema(description = "备注")
    private String remark;

    @ExcelProperty(value = "orderTime(下单时间)")
    @Schema(description = "下单时间")
    private String createTime;

    @ConvertType("scriptStatus")
    @ExcelProperty(value = "scriptStatus(凭证打印状态)",converter = IntegerDictsConverter.class)
    @Schema(description = "凭证打印状态")
    private Integer printStatus;

    @ExcelProperty(value = "printScriptTime(打印凭证时间)")
    @Schema(description = "打印凭证时间")
    private LocalDateTime printTime;
}

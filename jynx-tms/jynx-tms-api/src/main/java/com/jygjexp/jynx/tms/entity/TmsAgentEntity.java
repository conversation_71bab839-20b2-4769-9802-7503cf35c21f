package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * 代理商
 *
 * <AUTHOR>
 * @date 2025-09-06 03:05:04
 */
@Data
@TableName("tms_agent")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "代理商")
public class TmsAgentEntity extends BaseLogicEntity<TmsAgentEntity> {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 用户id
     */
    @Schema(description = "用户id")
    private Long userId;

    /**
     * 代理商编码
     */
    @NotBlank(message = "Agent code cannot empty")
    @Schema(description = "代理商编码")
    private String code;

    /**
     * 代理商名称
     */
    @NotBlank(message = "Agent name cannot empty")
    @Schema(description = "代理商名称")
    private String name;

    /**
     * 联系人
     */
    @Schema(description = "联系人")
    private String linkman;

    /**
     * 联系人电话
     */
    @Schema(description = "联系人电话")
    private String phone;

    /**
     * 代理商等级
     */
    @NotNull(message = "Agent level cannot empty")
    @Schema(description = "代理商等级")
    private Integer level;

    /**
     * 状态 0:禁用;1:启用
     */
    @Schema(description = "状态 0:禁用;1:启用")
    private Integer status;
}
package com.jygjexp.jynx.tms.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@Getter
@Setter
@NoArgsConstructor
public class StoreOrderPayOnline {

    @Schema(description = "主单id")
    @NotNull
    private Long id;

    @Schema(description = "渠道类型:BALANCE_AMOUNT:客户余额 ALIPAY_QR:支付宝 WX_NATIVE:微信支付 CREDIT_PAY:信用卡")
    @NotNull
    private String payType;

    @Schema(description = "成功回调页面")
    private String successPage;

    @Schema(description = "取消回调页面")
    private String cancelPage;
}

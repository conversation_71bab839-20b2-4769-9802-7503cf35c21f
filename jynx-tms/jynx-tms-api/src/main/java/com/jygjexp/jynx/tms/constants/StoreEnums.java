package com.jygjexp.jynx.tms.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

public interface StoreEnums {

    interface AddressBook {
        @AllArgsConstructor
        @Getter
        enum Type{
            EXPRESS(0,"快递");
            private final Integer value;
            private final String name;
        }

        @AllArgsConstructor
        @Getter
        enum SubType{
            SHIPPER(0,"发货");
            private final Integer value;
            private final String name;
        }
    }

    interface StoreOrder{

        @AllArgsConstructor
        @Getter
        enum EncFlagTypeEnum{
            INO_MMEDIATE_PUSH(0,"不核销"),
            IMMEDIATE_PUSH(1,"立即核销");
            private final Integer value;
            private final String name;
        }

        @AllArgsConstructor
        @Getter
        enum SendType{
            DELIVERY_TO_STORE(0,"送货到店"),
            COLLECTING_FROM_NB(5,"NB上门揽收"),
            COLLECTING_FROM_PROVIDER(10,"服务商上门揽收");
            private final Integer value;
            private final String name;
        }

        @AllArgsConstructor
        @Getter
        enum PayType{
            ONLINE(0,"线上支付"),
            OFFLINE(5,"线下支付");
            private final Integer value;
            private final String name;
        }

        @AllArgsConstructor
        @Getter
        enum OrderStatus{
            AWAITING_SHIPMENT(0,"待发货","AWAITING_SHIPMENT"),
            STORE_PICKUP(5,"门店取货","STORE_PICKUP"),
            PROVIDER_PICKUP(10,"服务商已取货","PROVIDER_PICKUP"),
            PRINT_SCRIPT(15,"已打印","PRINT_SCRIPT"), // 面单已打印
            IN_TRANSIT(20,"运输中","IN_TRANSIT"),
            COMPLETED(25,"已完成","COMPLETED"),
            EXCEPTION(30,"订单异常","EXCEPTION"),
            CANCEL(35,"已取消","CANCEL"),
            REFUND(40,"已退款","REFUND");
            private final Integer value;
            private final String name;
            private final String eName;
        }

        @AllArgsConstructor
        @Getter
        enum ExternalType{
            NB(0,"NB系统推单"),
            PACKET(1,"小包系统推单");
            private final Integer value;
            private final String name;
        }

        @AllArgsConstructor
        @Getter
        enum PrintStatus{
            UNPRINTED(0,"未打印"),
            PRINTED(1,"已打印");
            private final Integer value;
            private final String name;
        }

        @AllArgsConstructor
        @Getter
        enum WriteOffFlag{
            UnWriteOff(0,"未核销"),
            WriteOff(1,"已核销");
            private final Integer value;
            private final String name;
        }

        @AllArgsConstructor
        @Getter
        enum SubFlag{
            MAIN(0,"主单"),
            SUB(1,"子单");
            private final Integer value;
            private final String name;
        }

        @AllArgsConstructor
        @Getter
        enum PayOnlineType{
            BALANCE_AMOUNT(0,"客户余额","BALANCE_AMOUNT"),
            ALIPAY_QR(5,"支付宝","ALIPAY_QR"),
            WX_NATIVE(10,"微信","WX_NATIVE"),
            CREDIT_PAY(15,"信用卡","CREDIT_PAY");
            private final Integer value;
            private final String name;
            private final String code;
        }

        @AllArgsConstructor
        @Getter
        enum PayStatus{
            PENDING_PAYMENT(0,"待支付"),
            PAYMENT(5,"已支付"),
            PAYMENT_FAILED(15,"支付失败");
            private final Integer value;
            private final String name;
        }

    }

    interface TemplateChangeRecord{
        @AllArgsConstructor
        @Getter
        enum OperationType{
            DISABLE(0,"停用"),
            ENABLE(1,"启用"),
            MODIFY(2,"修改"),
            DELETE(3,"删除");
            private final Integer value;
            private final String name;
        }
    }

    interface StoreBalanceRecord{

        @AllArgsConstructor
        @Getter
        enum BusinessType{
            RECHARGE_ONLINE(0,"充值-线上"),
            RECHARGE_OFFLINE(5,"充值-线下"),
            ORDER_DEDUCT(10,"下单扣除"),
            OFFLINE_DEDUCT(12,"线下支付"),
            PAYMENT_ONLINE(13,"在线支付"),
            REFUND(15, "退款"),
            INCREASE(20,"手工调账-上调"),
            DECREASE(25,"手工调账-下调");
            private final Integer value;
            private final String name;
        }

        @AllArgsConstructor
        @Getter
        enum SubType{
            RECHARGE(0,"充值"),
            CONSUME(1,"消费");
            private final Integer value;
            private final String name;
        }
    }

    interface PackageSystem{
        @AllArgsConstructor
        @Getter
        enum PackageOrderStatus{
            PACKAGE_500("500","末端提取",""),
            PACKAGE_501("501","末端转运",""),
            PACKAGE_510("510","开始派送","Item out for delivery"),
            PACKAGE_512("512","到达待取",""),
            PACKAGE_520("520","签收","Delivered"),
            PACKAGE_511("511","派送失败",""),
            PACKAGE_535("535","地址错误",""),
            PACKAGE_542("542","退件到仓",""),
            PACKAGE_548("548","包裹丢失","");
            private final String value;
            private final String name;
            private final String eName;
        }
    }

    interface StoreMessageTrace{
        @AllArgsConstructor
        @Getter
        enum BusinessSubType{
            ORDER_EMAIL(0,"下单邮件提醒","下单邮件已发送!","The Order Email Has Been Send!"),
            ORDER_SUCCESS(5,"下单成功通知","订单已成功下单!","The Order Has Been Successfully Placed!"),
            ORDER_TRANSPORT(10,"货物运输通知","商家已取货!","The Merchant Has Picked Up The Goods!"),
            ORDER_SIGNED(15,"货物签收通知","订单已签收!","The Order Has Been Signed For!"),
            ORDER_EXCEPTION(20,"订单异常通知","订单异常!","Order Exception!"),
            ORDER_CANCEL(25,"订单取消通知","订单已取消!","Order Cancelled!");
            private final Integer value;
            private final String name;
            private final String context;
            private final String enContext;

            // 使用Map缓存枚举值，提高查找效率
            private static final Map<Integer, BusinessSubType> VALUE_MAP = new HashMap<>();

            static {
                // 初始化时将所有枚举值放入Map
                for (BusinessSubType type : values()) {
                    VALUE_MAP.put(type.value, type);
                }
            }
            public static BusinessSubType Lookup(Integer value) {
                if (value == null) {
                    return null;
                }
                return VALUE_MAP.get(value);
            }
        }

        @AllArgsConstructor
        @Getter
        enum MainType{
            STORE_ORDER(0,"快递订单业务");
            private final Integer value;
            private final String name;
        }

        @AllArgsConstructor
        @Getter
        enum ReadFlag{
            UN_READ(0,"未读"),
            READ(1,"已读");
            private final Integer value;
            private final String name;
        }
    }

    interface StoreCustomer{

        @AllArgsConstructor
        @Getter
        enum Level {
            NORMAL(0,"NORMAL"),
            VIP1(1,"VIP1"),
            VIP2(2,"VIP2"),
            VIP3(3,"VIP3");
            private final Integer value;
            private final String name;
        }
    }

    interface StoreProfitConfig{

        @AllArgsConstructor
        @Getter
        enum BlindBoxFlag {
            NOT_ENABLE(0,"否"),
            ENABLE(1,"是");
            private final Integer value;
            private final String name;
        }

        @AllArgsConstructor
        @Getter
        enum RuleType {
            PERCENTAGE(0,"百分比"),
            FIXED_VALUE(1,"固定值"),
            ABSOLUTE_PRICE(2,"绝对价格");
            private final Integer value;
            private final String name;
        }

        @AllArgsConstructor
        @Getter
        enum Unit {
            PERCENTAGE(1,"百分比"),
            FIXED_VALUE(1,"固定值"),
            ABSOLUTE_PRICE(2,"绝对价格");
            private final Integer value;
            private final String name;
        }




    }

    interface StorePromotionCode{
        @AllArgsConstructor
        @Getter
        enum Repeatable {
            UN_REPEATABLE(0,"不可重复使用"),
            REPEATABLE(1,"可重复使用");
            private final Integer value;
            private final String name;
        }

        @AllArgsConstructor
        @Getter
        enum DiscountType {
            ENOUGH_DISCOUNT(0,"满减"),
            DISCOUNT(1,"打折");
            private final Integer value;
            private final String name;
        }

        @AllArgsConstructor
        @Getter
        enum CommissionType {
            FIXED_AMOUNT(0,"固定金额"),
            FIXED_RATIO(1,"固定比例");
            private final Integer value;
            private final String name;
        }

        @AllArgsConstructor
        @Getter
        enum FirstUseCommissionType {
            UN_ENABLE(0,"否"),
            ENABLE(1,"是");
            private final Integer value;
            private final String name;
        }
    }

    interface StoreOrderEmail{
        @AllArgsConstructor
        @Getter
        enum EmailType{
            ORDER_CREATE(0,"下单成功提醒"),
            ORDER_VERIFIED(5,"核销成功通知"),
            ORDER_SHIPPING(10,"货物运输通知"),
            ORDER_CONFIRMATION(15,"货物签收通知"),
            ORDER_EXCEPTION(20,"订单异常通知"),
            ORDER_CANCEL(25,"订单取消通知");
            private final Integer value;
            private final String name;
        }
    }

    interface PayOrder{

        @AllArgsConstructor
        @Getter
        enum PayStatus{
            PENDING(0,"待支付"),
            SUCCESS(2,"支付成功(IOT)"),
            COMPLETE(3,"支付成功(Stripe)");
            private final Integer value;
            private final String name;
        }


    }
}

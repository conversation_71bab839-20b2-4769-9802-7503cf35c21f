/*
 *
 *      Copyright (c) 2018-2025, jynx All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the pig4cloud.com developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: jynx
 *
 */

package com.jygjexp.jynx.admin.api.feign;

import com.jygjexp.jynx.admin.api.dto.SysUserAddDto;
import com.jygjexp.jynx.admin.api.dto.UserDTO;
import com.jygjexp.jynx.admin.api.dto.UserInfo;
import com.jygjexp.jynx.admin.api.entity.SysUser;
import com.jygjexp.jynx.common.core.constant.ServiceNameConstants;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.feign.annotation.NoToken;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/6/22
 */
@FeignClient(contextId = "remoteUserService", value = ServiceNameConstants.UPMS_SERVICE)
public interface RemoteUserService {

    /**
     * 通过用户名查询用户、角色信息
     *
     * @param username 用户名
     * @return R
     */
    @NoToken
    @GetMapping("/user/info/{username}")
    R<UserInfo> info(@PathVariable("username") String username);


    /**
     * 根据手机号查询用户、角色信息
     *
     * @return 用户信息
     */
    @NoToken
    @GetMapping("/user/getUserInfoByPhone/{phone}")
    R<UserInfo> getUserInfoByPhone(@PathVariable String phone);

    /**
     * 根据用户ID获取用户
     *
     * @param userIds ID
     * @return SysUser
     */
    @GetMapping("/user/list")
    R<List<SysUser>> getUserById(@RequestParam("userIds") List<Long> userIds);

    /**
     * 根据用户ID获取用户
     *
     * @param id ID
     * @return SysUser
     */

    @GetMapping("/user/listById/{id}")
    R<SysUser> getOneUserById(@PathVariable Long id);

    /**
     * 通过社交账号或手机号查询用户、角色信息
     *
     * @param inStr appid@code
     * @return
     */
    @NoToken
    @GetMapping("/social/info/{inStr}")
    R<UserInfo> social(@PathVariable("inStr") String inStr);

    /**
     * 查询上级部门的用户信息
     *
     * @param username 用户名
     * @return R
     */
    @GetMapping("/user/ancestor/{username}")
    R<List<SysUser>> ancestorUsers(@PathVariable("username") String username);

    /**
     * 锁定用户
     *
     * @param username 用户名
     * @return
     */
    @NoToken
    @PutMapping("/user/lock/{username}")
    R<Boolean> lockUser(@PathVariable("username") String username);

    /**
     * 根据角色ID查询用户列表
     *
     * @param roleIdList 角色ID列表
     * @return 用户ID列表
     */
    @GetMapping("/user/getUserIdListByRoleIdList")
    R<List<Long>> getUserIdListByRoleIdList(@RequestParam("roleIdList") List<Long> roleIdList);

    /**
     * 根据部门ID列表获取用户ID列表接口
     *
     * @param deptIdList 部门ID列表
     * @return 用户ID列表
     */
    @GetMapping("/user/getUserIdListByDeptIdList")
    R<List<SysUser>> getUserIdListByDeptIdList(@RequestParam("deptIdList") List<Long> deptIdList);

    /**
     * 通过用户名查询用户列表
     *
     * @param userName 用户名
     * @return 用户列表
     */
    @GetMapping("/user/getUserListByUserName")
    R<List<SysUser>> getUserListByUserName(@RequestParam("username") String userName);

    /**
     * 注册用户
     *
     * @param userDTO 用户信息
     * @return success/false
     */
    @NoToken
    @PostMapping("/register/user")
    R<Boolean> registerUser(@RequestBody UserDTO userDTO);

    @NoToken
    @GetMapping(value = {"/user/getUserByRoleCodesAndPhone"})
    R<UserInfo> getInfoByRoleAndPhone(@RequestParam("roleCodes") List<String> roleCodes, @RequestParam("phone") String phone);

    @PostMapping("/user/register")
    @NoToken
    R<SysUser> register(@RequestBody SysUserAddDto dto);

    /**
     * 启用用户
     *
     * @param ids
     * @return
     */
    @PutMapping("/user/enables")
    R<Boolean> userEnables(@RequestBody List<Long> ids);
}
